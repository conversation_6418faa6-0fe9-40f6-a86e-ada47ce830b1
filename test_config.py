#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG压力测试配置文件
可以在这里修改测试参数，无需修改主要代码
"""

# RAG接口配置
RAG_CONFIG = {
    "api_url": "http://**************:3005/api/v1/chat/completions",
    "api_key": "Bearer wisegpt-xUY1az1OrsRiNFbGYnbuOSPPTL6zXj5slj9Hx0ICG0QmD35b5n29M4CJ76bW",
    "host": "http://**************:3005",
    "timeout": 200  # 请求超时时间(秒)
}

# 测试场景配置
TEST_SCENARIOS = [
    {
        "name": "基准测试",
        "description": "单用户基准性能测试",
        "users": 1,
        "spawn_rate": 1,
        "run_time": 100,
        "enabled": True
    },
    {
        "name": "轻负载测试", 
        "description": "轻度负载下的性能表现",
        "users": 5,
        "spawn_rate": 1,
        "run_time": 500,
        "enabled": True
    },
    {
        "name": "中等负载测试",
        "description": "中等负载下的性能表现", 
        "users": 10,
        "spawn_rate": 2,
        "run_time": 700,
        "enabled": True
    },
    {
        "name": "高负载测试",
        "description": "高负载下的性能表现",
        "users": 20,
        "spawn_rate": 4,
        "run_time": 1400,
        "enabled": True
    }
    # {
    #     "name": "压力测试",
    #     "description": "压力负载下的性能表现",
    #     "users": 50,
    #     "spawn_rate": 5,
    #     "run_time": 300,
    #     "enabled": True
    # },
    # {
    #     "name": "极限测试",
    #     "description": "极限负载下的性能表现",
    #     "users": 100,
    #     "spawn_rate": 10,
    #     "run_time": 300,
    #     "enabled": False  # 默认关闭，避免对服务器造成过大压力
    # }
]

# 测试数据配置
TEST_DATA_CONFIG = {
    # 简单问题 - 单一概念查询
    "simple_questions": [
        "什么是支付机构？",
        "支付业务许可证是什么？",
        "网络支付包括哪些类型？",
        "预付卡是什么？",
        "银行卡收单业务是什么？",
        "支付机构需要哪些许可？",
        "中国人民银行的作用是什么？",
        "支付业务的监管要求有哪些？",
        "什么是第三方支付？",
        "支付机构的资质要求是什么？"
    ],
    
    # 中等复杂度问题 - 需要推理分析
    "medium_questions": [
        "支付机构与银行的区别是什么？如何进行资金转移？",
        "申请支付业务许可证需要满足哪些条件和提交哪些材料？",
        "支付机构的业务范围包括哪些？各有什么特点？",
        "支付机构如何进行风险管理和反洗钱工作？",
        "支付业务许可证的有效期和续展流程是怎样的？",
        "支付机构变更重大事项需要履行哪些程序？",
        "支付机构的资金安全保障措施有哪些？",
        "支付机构如何保护消费者权益？",
        "支付机构的备付金管理制度是怎样的？",
        "支付机构如何处理客户投诉和纠纷？"
    ],
    
    # 复杂问题 - 多步骤分析推理
    "complex_questions": [
        "请详细分析支付机构在金融体系中的地位和作用，以及与传统银行业务的协同关系？",
        "支付机构在数字经济发展中扮演什么角色？如何平衡创新与风险管控？",
        "从监管角度分析，支付机构面临的主要合规挑战有哪些？如何应对？",
        "支付机构的商业模式有哪些？盈利点在哪里？面临哪些竞争压力？",
        "国际支付机构与国内支付机构在业务模式和监管要求上有什么差异？",
        "支付机构如何在保障资金安全的同时提升用户体验和服务效率？",
        "分析支付机构在跨境支付、普惠金融等领域的发展机遇和挑战？",
        "支付机构的技术架构和安全体系应该如何设计以满足监管要求？",
        "支付机构如何应对金融科技发展带来的机遇和挑战？",
        "从风险管理角度，支付机构应该建立怎样的内控体系？"
    ],
    
    # 参数组合配置
    "param_combinations": [
        # {"stream": False, "detail": False, "weight": 3},  # 最常用
        # {"stream": False, "detail": True, "weight": 2},   # 常用
        {"stream": True, "detail": False, "weight": 1},   # 较少用
        # {"stream": True, "detail": True, "weight": 1}     # 较少用
    ],
    
    # 任务权重配置 - 只使用简单问题
    "task_weights": {
        "simple": 1,    # 只使用简单问题
        "medium": 0,    # 不使用中等问题
        "complex": 0    # 不使用复杂问题
    }
}

# 性能指标阈值配置
PERFORMANCE_THRESHOLDS = {
    "response_time": {
        "excellent": 2.0,    # 优秀: <2秒
        "good": 5.0,         # 良好: <5秒
        "acceptable": 10.0,  # 可接受: <10秒
        "poor": 20.0         # 较差: <20秒
    },
    "first_byte_time": {
        "excellent": 0.5,    # 优秀: <0.5秒
        "good": 1.0,         # 良好: <1秒
        "acceptable": 2.0,   # 可接受: <2秒
        "poor": 5.0          # 较差: <5秒
    },
    "success_rate": {
        "excellent": 99.5,   # 优秀: >99.5%
        "good": 99.0,        # 良好: >99%
        "acceptable": 95.0,  # 可接受: >95%
        "poor": 90.0         # 较差: >90%
    },
    "qps": {
        "target_min": 1.0,   # 最低QPS目标
        "target_good": 5.0,  # 良好QPS目标
        "target_excellent": 10.0  # 优秀QPS目标
    }
}

# 报告配置
REPORT_CONFIG = {
    "save_detailed_csv": True,      # 是否保存详细CSV数据
    "generate_charts": True,        # 是否生成图表
    "chart_dpi": 300,              # 图表分辨率
    "include_percentiles": [50, 75, 90, 95, 99],  # 包含的百分位数
    "real_time_update_interval": 10,  # 实时更新间隔(秒)
}

# Locust配置
LOCUST_CONFIG = {
    "web_host": "0.0.0.0",
    "web_port": 8089,
    "csv_prefix": "rag_test",
    "html_report": True,
    "logfile": "locust.log",
    "loglevel": "INFO"
}

# 测试环境配置
ENVIRONMENT_CONFIG = {
    "test_data_randomization": True,   # 是否随机化测试数据
    "user_think_time_min": 1,         # 用户思考时间最小值(秒)
    "user_think_time_max": 3,         # 用户思考时间最大值(秒)
    "connection_timeout": 30,         # 连接超时时间(秒)
    "request_timeout": 60,            # 请求超时时间(秒)
    "retry_count": 3,                 # 失败重试次数
    "scenario_interval": 30,          # 测试场景间隔时间(秒)
}

def get_enabled_scenarios():
    """获取启用的测试场景"""
    return [scenario for scenario in TEST_SCENARIOS if scenario.get("enabled", True)]

def get_question_by_complexity(complexity="mixed"):
    """根据复杂度获取问题"""
    import random
    
    if complexity == "simple":
        return random.choice(TEST_DATA_CONFIG["simple_questions"])
    elif complexity == "medium":
        return random.choice(TEST_DATA_CONFIG["medium_questions"])
    elif complexity == "complex":
        return random.choice(TEST_DATA_CONFIG["complex_questions"])
    else:  # mixed
        all_questions = (
            TEST_DATA_CONFIG["simple_questions"] + 
            TEST_DATA_CONFIG["medium_questions"] + 
            TEST_DATA_CONFIG["complex_questions"]
        )
        return random.choice(all_questions)

def get_weighted_params():
    """根据权重获取参数组合"""
    import random
    
    params = TEST_DATA_CONFIG["param_combinations"]
    weights = [p["weight"] for p in params]
    selected = random.choices(params, weights=weights, k=1)[0]
    return {k: v for k, v in selected.items() if k != "weight"}

def evaluate_performance(metric_name, value):
    """评估性能指标"""
    if metric_name not in PERFORMANCE_THRESHOLDS:
        return "unknown"
    
    thresholds = PERFORMANCE_THRESHOLDS[metric_name]
    
    if metric_name == "success_rate":
        # 成功率越高越好
        if value >= thresholds["excellent"]:
            return "excellent"
        elif value >= thresholds["good"]:
            return "good"
        elif value >= thresholds["acceptable"]:
            return "acceptable"
        elif value >= thresholds["poor"]:
            return "poor"
        else:
            return "critical"
    else:
        # 响应时间等越低越好
        if value <= thresholds["excellent"]:
            return "excellent"
        elif value <= thresholds["good"]:
            return "good"
        elif value <= thresholds["acceptable"]:
            return "acceptable"
        elif value <= thresholds["poor"]:
            return "poor"
        else:
            return "critical"

if __name__ == "__main__":
    # 配置文件测试
    print("RAG压力测试配置")
    print("="*50)
    print(f"API地址: {RAG_CONFIG['api_url']}")
    print(f"启用的测试场景数: {len(get_enabled_scenarios())}")
    print(f"测试问题总数: {len(TEST_DATA_CONFIG['simple_questions']) + len(TEST_DATA_CONFIG['medium_questions']) + len(TEST_DATA_CONFIG['complex_questions'])}")
    
    # 示例使用
    print(f"\n示例问题: {get_question_by_complexity('simple')}")
    print(f"示例参数: {get_weighted_params()}")
    print(f"性能评估示例: 响应时间3秒 -> {evaluate_performance('response_time', 3.0)}")
