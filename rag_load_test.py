#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG接口压力测试脚本
使用Locust框架对RAG_data_get接口进行性能测试
测试指标：首字延迟、吞吐量、QPS、最大并发数等
"""

import os
import json
import time
import random
import threading
from datetime import datetime
from typing import Dict, List, Any
import numpy as np
import pandas as pd
from tqdm import tqdm
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# Locust相关导入
from locust import HttpUser, task, between, events
from locust.env import Environment
from locust.stats import stats_printer, stats_history
from locust.log import setup_logging
from locust.runners import LocalRunner
import gevent

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 导入配置
try:
    from test_config import (
        RAG_CONFIG, TEST_SCENARIOS, TEST_DATA_CONFIG,
        PERFORMANCE_THRESHOLDS, get_enabled_scenarios,
        get_question_by_complexity, get_weighted_params,
        evaluate_performance
    )
    # 使用配置文件中的设置
    RAG_POST_API_URL = RAG_CONFIG["api_url"]
    RAG_POST_API_KEY = RAG_CONFIG["api_key"]
    RAG_HOST = RAG_CONFIG["host"]
    USE_CONFIG_FILE = True
except ImportError:
    # 如果没有配置文件，使用默认配置
    RAG_POST_API_URL = "http://**************:3005/api/v1/chat/completions"
    RAG_POST_API_KEY = "Bearer wisegpt-xUY1az1OrsRiNFbGYnbuOSPPTL6zXj5slj9Hx0ICG0QmD35b5n29M4CJ76bW"
    RAG_HOST = "http://**************:3005"
    USE_CONFIG_FILE = False
    print("警告: 未找到test_config.py配置文件，使用默认配置")


class TestDataManager:
    """测试数据管理器"""

    def __init__(self):
        if USE_CONFIG_FILE:
            # 使用配置文件中的数据
            self.simple_questions = TEST_DATA_CONFIG["simple_questions"]
            self.medium_questions = TEST_DATA_CONFIG["medium_questions"]
            self.complex_questions = TEST_DATA_CONFIG["complex_questions"]
            self.param_combinations = [
                {k: v for k, v in combo.items() if k != "weight"}
                for combo in TEST_DATA_CONFIG["param_combinations"]
            ]
        else:
            # 使用默认数据
            self.simple_questions = [
                "什么是支付机构？",
                "支付业务许可证是什么？",
                "网络支付包括哪些类型？",
                "预付卡是什么？",
                "银行卡收单业务是什么？",
                "支付机构需要哪些许可？",
                "中国人民银行的作用是什么？",
                "支付业务的监管要求有哪些？"
            ]

            self.medium_questions = [
                "支付机构与银行的区别是什么？如何进行资金转移？",
                "申请支付业务许可证需要满足哪些条件和提交哪些材料？",
                "支付机构的业务范围包括哪些？各有什么特点？",
                "支付机构如何进行风险管理和反洗钱工作？",
                "支付业务许可证的有效期和续展流程是怎样的？",
                "支付机构变更重大事项需要履行哪些程序？",
                "支付机构的资金安全保障措施有哪些？",
                "支付机构如何保护消费者权益？"
            ]

            self.complex_questions = [
                "请详细分析支付机构在金融体系中的地位和作用，以及与传统银行业务的协同关系？",
                "支付机构在数字经济发展中扮演什么角色？如何平衡创新与风险管控？",
                "从监管角度分析，支付机构面临的主要合规挑战有哪些？如何应对？",
                "支付机构的商业模式有哪些？盈利点在哪里？面临哪些竞争压力？",
                "国际支付机构与国内支付机构在业务模式和监管要求上有什么差异？",
                "支付机构如何在保障资金安全的同时提升用户体验和服务效率？",
                "分析支付机构在跨境支付、普惠金融等领域的发展机遇和挑战？",
                "支付机构的技术架构和安全体系应该如何设计以满足监管要求？"
            ]

            # 参数组合
            self.param_combinations = [
                {"stream": False, "detail": False},
                {"stream": False, "detail": True},
                {"stream": True, "detail": False},
                {"stream": True, "detail": True}
            ]

    def get_random_question(self, complexity="mixed"):
        """获取随机问题"""
        if USE_CONFIG_FILE:
            return get_question_by_complexity(complexity)
        else:
            # 使用默认逻辑
            if complexity == "simple":
                return random.choice(self.simple_questions)
            elif complexity == "medium":
                return random.choice(self.medium_questions)
            elif complexity == "complex":
                return random.choice(self.complex_questions)
            else:  # mixed
                all_questions = self.simple_questions + self.medium_questions + self.complex_questions
                return random.choice(all_questions)

    def get_random_params(self):
        """获取随机参数组合"""
        if USE_CONFIG_FILE:
            return get_weighted_params()
        else:
            return random.choice(self.param_combinations)


class PerformanceMetrics:
    """性能指标收集器"""
    
    def __init__(self):
        self.response_times = []
        self.first_byte_times = []
        self.success_count = 0
        self.failure_count = 0
        self.start_time = None
        self.end_time = None
        self.concurrent_users = 0
        self.detailed_results = []
        self.lock = threading.Lock()
    
    def add_result(self, response_time, first_byte_time, success, concurrent_users, question_type, params):
        """添加测试结果"""
        with self.lock:
            self.response_times.append(response_time)
            if first_byte_time:
                self.first_byte_times.append(first_byte_time)
            
            if success:
                self.success_count += 1
            else:
                self.failure_count += 1
            
            self.concurrent_users = max(self.concurrent_users, concurrent_users)
            
            self.detailed_results.append({
                'timestamp': datetime.now(),
                'response_time': response_time,
                'first_byte_time': first_byte_time,
                'success': success,
                'concurrent_users': concurrent_users,
                'question_type': question_type,
                'params': params
            })
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.response_times:
            return {}
        
        total_requests = self.success_count + self.failure_count
        duration = (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0
        
        stats = {
            'total_requests': total_requests,
            'success_requests': self.success_count,
            'failed_requests': self.failure_count,
            'success_rate': (self.success_count / total_requests * 100) if total_requests > 0 else 0,
            'avg_response_time': np.mean(self.response_times),
            'min_response_time': np.min(self.response_times),
            'max_response_time': np.max(self.response_times),
            'p50_response_time': np.percentile(self.response_times, 50),
            'p95_response_time': np.percentile(self.response_times, 95),
            'p99_response_time': np.percentile(self.response_times, 99),
            'qps': total_requests / duration if duration > 0 else 0,
            'max_concurrent_users': self.concurrent_users,
            'test_duration': duration
        }
        
        if self.first_byte_times:
            stats.update({
                'avg_first_byte_time': np.mean(self.first_byte_times),
                'min_first_byte_time': np.min(self.first_byte_times),
                'max_first_byte_time': np.max(self.first_byte_times),
                'p95_first_byte_time': np.percentile(self.first_byte_times, 95)
            })
        
        return stats
    
    def save_to_csv(self, filename):
        """保存详细结果到CSV文件"""
        df = pd.DataFrame(self.detailed_results)
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"详细测试结果已保存到: {filename}")


# 全局对象
test_data_manager = TestDataManager()
performance_metrics = PerformanceMetrics()


class RAGUser(HttpUser):
    """RAG压力测试用户类"""
    
    host = RAG_HOST
    wait_time = between(1, 3)  # 请求间隔时间
    
    def on_start(self):
        """用户开始时的初始化"""
        self.client.headers.update({
            'Authorization': RAG_POST_API_KEY,
            'Content-Type': 'application/json'
        })
        self.question_counts = {'simple': 0, 'medium': 0, 'complex': 0}
    
    def make_request(self, question, params, question_type):
        """发送RAG请求并收集性能指标"""
        payload = {
            "chatId": f"test_{self.environment.runner.user_count}_{random.randint(1000, 9999)}",
            "stream": params["stream"],
            "detail": params["detail"],
            "variables": {"uid": f"test_user_{random.randint(1000, 9999)}", "name": "RAG_LoadTest"},
            "messages": [{"content": question, "role": "user"}]
        }
        
        start_time = time.time()
        first_byte_time = None
        success = False
        
        try:
            with self.client.post(
                "/api/v1/chat/completions",
                json=payload,
                catch_response=True,
                stream=params["stream"]
            ) as response:
                
                # 记录首字节时间
                if not first_byte_time:
                    first_byte_time = time.time() - start_time
                
                if response.status_code == 200:
                    success = True
                    # 处理流式响应
                    if params["stream"]:
                        content_length = 0
                        for chunk in response.iter_content(chunk_size=1024):
                            if chunk:
                                content_length += len(chunk)
                        response.success()
                    else:
                        # 非流式响应
                        try:
                            result = response.json()
                            if 'choices' in result and len(result['choices']) > 0:
                                response.success()
                            else:
                                response.failure("Empty or invalid response")
                                success = False
                        except json.JSONDecodeError:
                            response.failure("Invalid JSON response")
                            success = False
                else:
                    response.failure(f"HTTP {response.status_code}")
                    success = False
                    
        except Exception as e:
            success = False
            print(f"请求异常: {str(e)}")
            
        # 记录性能指标
        end_time = time.time()
        response_time = end_time - start_time
        
        performance_metrics.add_result(
            response_time=response_time,
            first_byte_time=first_byte_time,
            success=success,
            concurrent_users=self.environment.runner.user_count if hasattr(self.environment, 'runner') else 1,
            question_type=question_type,
            params=params
        )
        
        self.question_counts[question_type] += 1
    
    @task(3)
    def simple_question_task(self):
        """简单问题测试任务"""
        question = test_data_manager.get_random_question("simple")
        params = test_data_manager.get_random_params()
        self.make_request(question, params, "simple")
    
    @task(2)
    def medium_question_task(self):
        """中等复杂度问题测试任务"""
        question = test_data_manager.get_random_question("medium")
        params = test_data_manager.get_random_params()
        self.make_request(question, params, "medium")
    
    @task(1)
    def complex_question_task(self):
        """复杂问题测试任务"""
        question = test_data_manager.get_random_question("complex")
        params = test_data_manager.get_random_params()
        self.make_request(question, params, "complex")
    
    def on_stop(self):
        """用户停止时的清理工作"""
        print(f"用户停止 - 问题统计: {self.question_counts}")


class LoadTestRunner:
    """压力测试执行器"""

    def __init__(self):
        self.results = {}

    def run_test_scenario(self, users, spawn_rate, run_time, scenario_name):
        """运行单个测试场景"""
        print(f"\n{'='*60}")
        print(f"开始执行测试场景: {scenario_name}")
        print(f"并发用户数: {users}, 启动速率: {spawn_rate}/秒, 运行时间: {run_time}秒")
        print(f"{'='*60}")

        # 重置性能指标
        global performance_metrics
        performance_metrics = PerformanceMetrics()
        performance_metrics.start_time = datetime.now()

        # 设置Locust环境
        env = Environment(user_classes=[RAGUser])
        env.create_local_runner()

        # 开始测试
        env.runner.start(users, spawn_rate=spawn_rate)

        # 运行指定时间
        start_time = time.time()
        with tqdm(total=run_time, desc=f"执行{scenario_name}") as pbar:
            while time.time() - start_time < run_time:
                time.sleep(1)
                pbar.update(1)

                # 实时显示统计信息
                if int(time.time() - start_time) % 10 == 0:  # 每10秒显示一次
                    current_stats = performance_metrics.get_statistics()
                    if current_stats:
                        pbar.set_postfix({
                            'QPS': f"{current_stats.get('qps', 0):.1f}",
                            'Avg RT': f"{current_stats.get('avg_response_time', 0):.2f}s",
                            'Success': f"{current_stats.get('success_rate', 0):.1f}%"
                        })

        # 停止测试
        env.runner.stop()
        performance_metrics.end_time = datetime.now()

        # 收集结果
        stats = performance_metrics.get_statistics()
        self.results[scenario_name] = stats

        # 显示结果
        self.print_scenario_results(scenario_name, stats)

        # 保存详细数据
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_filename = f"rag_test_{scenario_name}_{timestamp}.csv"
        performance_metrics.save_to_csv(csv_filename)

        return stats

    def print_scenario_results(self, scenario_name, stats):
        """打印场景测试结果"""
        print(f"\n{'-'*50}")
        print(f"测试场景: {scenario_name} - 结果汇总")
        print(f"{'-'*50}")
        print(f"总请求数: {stats.get('total_requests', 0)}")
        print(f"成功请求数: {stats.get('success_requests', 0)}")
        print(f"失败请求数: {stats.get('failed_requests', 0)}")
        print(f"成功率: {stats.get('success_rate', 0):.2f}%")
        print(f"QPS: {stats.get('qps', 0):.2f}")
        print(f"最大并发用户数: {stats.get('max_concurrent_users', 0)}")
        print(f"测试持续时间: {stats.get('test_duration', 0):.2f}秒")
        print(f"\n响应时间统计:")
        print(f"  平均响应时间: {stats.get('avg_response_time', 0):.3f}秒")
        print(f"  最小响应时间: {stats.get('min_response_time', 0):.3f}秒")
        print(f"  最大响应时间: {stats.get('max_response_time', 0):.3f}秒")
        print(f"  P50响应时间: {stats.get('p50_response_time', 0):.3f}秒")
        print(f"  P95响应时间: {stats.get('p95_response_time', 0):.3f}秒")
        print(f"  P99响应时间: {stats.get('p99_response_time', 0):.3f}秒")

        if 'avg_first_byte_time' in stats:
            print(f"\n首字节时间统计:")
            print(f"  平均首字节时间: {stats.get('avg_first_byte_time', 0):.3f}秒")
            print(f"  最小首字节时间: {stats.get('min_first_byte_time', 0):.3f}秒")
            print(f"  最大首字节时间: {stats.get('max_first_byte_time', 0):.3f}秒")
            print(f"  P95首字节时间: {stats.get('p95_first_byte_time', 0):.3f}秒")

    def run_comprehensive_test(self):
        """运行综合压力测试"""
        print("开始RAG接口综合压力测试")
        print("="*80)

        # 获取测试场景配置
        if USE_CONFIG_FILE:
            test_scenarios = get_enabled_scenarios()
        else:
            # 使用默认测试场景
            test_scenarios = [
                {"name": "基准测试", "users": 1, "spawn_rate": 1, "run_time": 60},
                {"name": "轻负载测试", "users": 5, "spawn_rate": 1, "run_time": 120},
                {"name": "中等负载测试", "users": 10, "spawn_rate": 2, "run_time": 180},
                {"name": "高负载测试", "users": 20, "spawn_rate": 4, "run_time": 180},
                {"name": "压力测试", "users": 50, "spawn_rate": 5, "run_time": 300},
            ]

        # 执行各个测试场景
        for scenario in test_scenarios:
            try:
                self.run_test_scenario(
                    users=scenario["users"],
                    spawn_rate=scenario["spawn_rate"],
                    run_time=scenario["run_time"],
                    scenario_name=scenario["name"]
                )
                # 场景间休息时间
                print(f"场景完成，休息30秒...")
                time.sleep(30)
            except Exception as e:
                print(f"测试场景 {scenario['name']} 执行失败: {str(e)}")
                continue

        # 生成综合报告
        # self.generate_comprehensive_report()

    def generate_comprehensive_report(self):
        """生成综合测试报告"""
        if not self.results:
            print("没有测试结果可生成报告")
            return

        print(f"\n{'='*80}")
        print("RAG接口压力测试 - 综合报告")
        print(f"{'='*80}")

        # 创建结果对比表
        df_results = pd.DataFrame(self.results).T

        # 显示关键指标对比
        key_metrics = ['max_concurrent_users', 'qps', 'avg_response_time', 'success_rate', 'p95_response_time']
        if not df_results.empty:
            print("\n关键性能指标对比:")
            print("-" * 80)
            for metric in key_metrics:
                if metric in df_results.columns:
                    print(f"{metric:20s}: {df_results[metric].to_dict()}")

        # 保存综合报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"rag_comprehensive_report_{timestamp}.csv"
        df_results.to_csv(report_filename, encoding='utf-8-sig')
        print(f"\n综合报告已保存到: {report_filename}")

        # 生成性能图表
        self.generate_performance_charts(df_results, timestamp)

    def generate_performance_charts(self, df_results, timestamp):
        """生成性能图表"""
        if df_results.empty:
            return

        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('RAG接口压力测试性能报告', fontsize=16, fontweight='bold')

        # QPS vs 并发用户数
        if 'qps' in df_results.columns and 'max_concurrent_users' in df_results.columns:
            axes[0, 0].plot(df_results['max_concurrent_users'], df_results['qps'], 'bo-', linewidth=2, markersize=8)
            axes[0, 0].set_xlabel('并发用户数')
            axes[0, 0].set_ylabel('QPS')
            axes[0, 0].set_title('QPS vs 并发用户数')
            axes[0, 0].grid(True, alpha=0.3)

        # 响应时间 vs 并发用户数
        if 'avg_response_time' in df_results.columns:
            axes[0, 1].plot(df_results['max_concurrent_users'], df_results['avg_response_time'], 'ro-', linewidth=2, markersize=8)
            if 'p95_response_time' in df_results.columns:
                axes[0, 1].plot(df_results['max_concurrent_users'], df_results['p95_response_time'], 'go-', linewidth=2, markersize=8, label='P95')
            axes[0, 1].set_xlabel('并发用户数')
            axes[0, 1].set_ylabel('响应时间 (秒)')
            axes[0, 1].set_title('响应时间 vs 并发用户数')
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)

        # 成功率 vs 并发用户数
        if 'success_rate' in df_results.columns:
            axes[1, 0].plot(df_results['max_concurrent_users'], df_results['success_rate'], 'go-', linewidth=2, markersize=8)
            axes[1, 0].set_xlabel('并发用户数')
            axes[1, 0].set_ylabel('成功率 (%)')
            axes[1, 0].set_title('成功率 vs 并发用户数')
            axes[1, 0].set_ylim(0, 105)
            axes[1, 0].grid(True, alpha=0.3)

        # 首字节时间对比
        if 'avg_first_byte_time' in df_results.columns:
            axes[1, 1].plot(df_results['max_concurrent_users'], df_results['avg_first_byte_time'], 'mo-', linewidth=2, markersize=8)
            axes[1, 1].set_xlabel('并发用户数')
            axes[1, 1].set_ylabel('首字节时间 (秒)')
            axes[1, 1].set_title('首字节时间 vs 并发用户数')
            axes[1, 1].grid(True, alpha=0.3)

        plt.tight_layout()
        chart_filename = f"rag_performance_charts_{timestamp}.png"
        plt.savefig(chart_filename, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"性能图表已保存到: {chart_filename}")


def main():
    """主函数"""
    print("RAG接口压力测试工具")
    print("="*50)

    # 首先测试接口连通性
    print("测试接口连通性...")
    try:
        import requests
        headers = {'Authorization': RAG_POST_API_KEY, 'Content-Type': 'application/json'}
        payload = {
            "chatId": "connectivity_test",
            "stream": False,
            "detail": False,
            "variables": {"uid": "test_user", "name": "ConnectivityTest"},
            "messages": [{"content": "什么是支付机构？", "role": "user"}]
        }
        response = requests.post(RAG_POST_API_URL, headers=headers, json=payload, timeout=30)
        if response.status_code == 200:
            print("✓ 接口连通性测试通过")
        else:
            print(f"✗ 接口连通性测试失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"✗ 接口连通性测试失败: {str(e)}")
        return

    # 创建测试执行器并运行综合测试
    runner = LoadTestRunner()

    print("\n选择测试模式:")
    print("1. 快速测试 (单场景)")
    print("2. 综合测试 (多场景)")
    print("3. 自定义测试")

    choice = input("请选择 (1-3): ").strip()

    if choice == "1":
        # 快速测试
        runner.run_test_scenario(users=10, spawn_rate=2, run_time=120, scenario_name="快速测试")
    elif choice == "2":
        # 综合测试
        runner.run_comprehensive_test()
    elif choice == "3":
        # 自定义测试
        try:
            users = int(input("并发用户数: "))
            spawn_rate = int(input("启动速率 (用户/秒): "))
            run_time = int(input("运行时间 (秒): "))
            scenario_name = input("测试场景名称: ") or "自定义测试"
            runner.run_test_scenario(users, spawn_rate, run_time, scenario_name)
        except ValueError:
            print("输入参数无效")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
