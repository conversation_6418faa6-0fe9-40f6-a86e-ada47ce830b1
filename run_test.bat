@echo off
chcp 65001 >nul
echo ========================================
echo RAG接口压力测试工具
echo ========================================
echo.

:MENU
echo 请选择操作:
echo 1. 检查依赖包
echo 2. 连通性测试
echo 3. 快速压力测试
echo 4. 启动Web界面
echo 5. 查看帮助
echo 6. 退出
echo.
set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" goto CHECK_DEPS
if "%choice%"=="2" goto CONNECTIVITY_TEST
if "%choice%"=="3" goto QUICK_TEST
if "%choice%"=="4" goto WEB_UI
if "%choice%"=="5" goto HELP
if "%choice%"=="6" goto EXIT
echo 无效选择，请重新输入
goto MENU

:CHECK_DEPS
echo.
echo 检查Python依赖包...
python -c "import locust, numpy, pandas, matplotlib, seaborn, tqdm, requests; print('✓ 所有依赖包已安装')" 2>nul
if errorlevel 1 (
    echo ❌ 缺少依赖包，正在安装...
    pip install locust numpy pandas matplotlib seaborn tqdm requests
    if errorlevel 1 (
        echo ❌ 依赖包安装失败，请手动安装
        pause
        goto MENU
    )
    echo ✓ 依赖包安装完成
)
pause
goto MENU

:CONNECTIVITY_TEST
echo.
echo 执行连通性测试...
python test_connectivity.py
pause
goto MENU

:QUICK_TEST
echo.
echo 选择测试模式:
echo 1. 简化测试 (只运行前4个场景，只问简单问题)
echo 2. 完整测试 (所有场景和问题类型)
echo.
set /p test_choice=请选择 (1-2):

if "%test_choice%"=="1" (
    echo.
    echo 启动简化压力测试...
    echo 配置: 前4个场景, 只问简单问题, stream=True detail=False
    python simple_rag_test.py
) else if "%test_choice%"=="2" (
    echo.
    echo 启动完整压力测试...
    python run_rag_test.py
) else (
    echo 无效选择
)
pause
goto MENU

:WEB_UI
echo.
echo 选择Web界面模式:
echo 1. 简化Web界面 (只问简单问题)
echo 2. 完整Web界面 (所有问题类型)
echo.
set /p web_choice=请选择 (1-2):

if "%web_choice%"=="1" (
    echo.
    echo 启动简化Locust Web界面...
    echo Web地址: http://localhost:8089
    echo 按 Ctrl+C 停止服务
    echo.
    python -m locust -f simple_rag_test.py --host http://**************:3005
) else if "%web_choice%"=="2" (
    echo.
    echo 启动完整Locust Web界面...
    echo Web地址: http://localhost:8089
    echo 按 Ctrl+C 停止服务
    echo.
    python -m locust -f rag_load_test.py --host http://**************:3005
) else (
    echo 无效选择
)
pause
goto MENU

:HELP
echo.
echo ========================================
echo RAG压力测试工具使用说明
echo ========================================
echo.
echo 文件说明:
echo   rag_load_test.py     - 主测试脚本
echo   run_rag_test.py      - 快速运行脚本
echo   test_config.py       - 配置文件
echo   test_connectivity.py - 连通性测试
echo   README_RAG_LoadTest.md - 详细说明文档
echo.
echo 测试流程:
echo   1. 首先运行连通性测试确认接口正常
echo   2. 选择合适的测试模式进行压力测试
echo   3. 查看生成的报告和图表分析结果
echo.
echo 测试模式:
echo   Web界面模式 - 可视化实时监控 (推荐)
echo   快速测试模式 - 自动化批量测试
echo   自定义模式 - 灵活配置测试参数
echo.
pause
goto MENU

:EXIT
echo 感谢使用RAG压力测试工具！
exit /b 0
