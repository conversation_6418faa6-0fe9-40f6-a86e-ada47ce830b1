#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
九天模型压力测试快速启动脚本
"""

import subprocess
import sys
import os

def run_jiutian_test():
    """运行九天模型压力测试"""
    print("="*60)
    print("九天模型压力测试快速启动")
    print("="*60)
    print("测试配置:")
    print("- 16卡GPU配置优化")
    print("- 4个测试场景: 基准(1用户) -> 轻负载(8用户) -> 中负载(16用户) -> 高负载(32用户)")
    print("- 关注指标: 最大并发数、最佳并发数、首字延迟、吞吐量")
    print("- 生成中文性能报告")
    print("="*60)
    
    try:
        # 直接运行九天模型测试脚本
        print("启动测试...")
        result = subprocess.run([
            sys.executable, "jiutian_load_test.py"
        ], cwd=os.getcwd(), capture_output=False)
        
        if result.returncode == 0:
            print("\n✓ 测试完成!")
        else:
            print(f"\n✗ 测试失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"✗ 运行测试时出错: {str(e)}")

def run_locust_web():
    """使用Locust Web界面运行测试"""
    print("="*60)
    print("启动九天模型Locust Web界面")
    print("="*60)
    print("将打开 http://localhost:8089")
    print("在Web界面中可以手动控制测试参数")
    print("="*60)
    
    try:
        # 使用九天模型测试脚本启动Locust Web界面
        print("启动Locust Web界面...")
        subprocess.run([
            sys.executable, "-m", "locust", 
            "-f", "jiutian_load_test.py",
            "--host", "http://**************:8000"
        ], cwd=os.getcwd())
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"✗ 启动Locust时出错: {str(e)}")

def main():
    """主函数"""
    print("九天模型压力测试快速启动工具")
    print("="*40)
    print("选择运行模式:")
    print("1. 自动运行综合测试 (推荐)")
    print("2. 启动Locust Web界面 (手动控制)")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            run_jiutian_test()
            break
        elif choice == "2":
            run_locust_web()
            break
        elif choice == "3":
            print("退出")
            break
        else:
            print("无效选择，请输入 1、2 或 3")

if __name__ == "__main__":
    main()
