# RAG接口压力测试工具

这是一个专门为RAG_data_get接口设计的压力测试工具，使用Locust框架实现，可以全面测试接口的性能指标。

## 功能特性

### 🎯 核心性能指标
- **首字延迟 (TTFB)**: 从发送请求到收到第一个字节的时间
- **完整响应时间**: 从发送请求到完整响应接收完毕的时间  
- **QPS**: 每秒处理的查询数量
- **吞吐量**: 单位时间内成功处理的请求数量
- **最大并发数**: 系统能够稳定处理的最大同时请求数
- **成功率**: 成功请求占总请求的百分比
- **响应时间分布**: P50、P95、P99等百分位数

### 🧪 测试场景
- **基准测试**: 单用户性能基线
- **轻负载测试**: 5用户并发
- **中等负载测试**: 10用户并发  
- **高负载测试**: 20用户并发
- **压力测试**: 50用户并发
- **极限测试**: 100用户并发

### 📊 测试数据
- **简单问题**: 单一概念查询 (权重3)
- **中等问题**: 需要推理的问题 (权重2)
- **复杂问题**: 多步骤分析问题 (权重1)
- **参数组合**: stream/detail的不同组合

## 文件结构

```
├── rag_load_test.py          # 主测试脚本
├── run_rag_test.py           # 快速运行脚本
├── test_config.py            # 配置文件
├── testRAG.ipynb             # Jupyter测试笔记本
└── README_RAG_LoadTest.md    # 说明文档
```

## 安装依赖

```bash
pip install locust numpy pandas matplotlib seaborn tqdm requests
```

## 使用方法

### 方法1: 快速运行脚本 (推荐)

```bash
python run_rag_test.py
```

选择测试模式:
1. **Web界面模式** - 可视化界面，实时监控 (推荐)
2. **无界面批量测试** - 自动执行多个测试场景
3. **单个自定义测试** - 快速执行单个测试
4. **Python脚本直接运行** - 使用内置测试流程

### 方法2: Locust Web界面

```bash
locust -f rag_load_test.py --host http://**************:3005
```

然后访问 http://localhost:8089 进行可视化测试

### 方法3: 无界面命令行

```bash
# 10用户，2/秒启动速率，运行3分钟
locust -f rag_load_test.py --host http://**************:3005 --headless -u 10 -r 2 -t 3m
```

### 方法4: Python脚本直接运行

```bash
python rag_load_test.py
```

## 配置说明

### 修改测试配置

编辑 `test_config.py` 文件可以修改:

- **接口配置**: API地址、密钥等
- **测试场景**: 并发数、运行时间等
- **测试数据**: 问题库、参数组合等
- **性能阈值**: 各项指标的评估标准

### 主要配置项

```python
# RAG接口配置
RAG_CONFIG = {
    "api_url": "http://**************:3005/api/v1/chat/completions",
    "api_key": "Bearer wisegpt-xUY1az1OrsRiNFbGYnbuOSPPTL6zXj5slj9Hx0ICG0QmD35b5n29M4CJ76bW",
    "host": "http://**************:3005"
}

# 测试场景配置
TEST_SCENARIOS = [
    {"name": "轻负载测试", "users": 5, "spawn_rate": 1, "run_time": 120},
    {"name": "中等负载测试", "users": 10, "spawn_rate": 2, "run_time": 180},
    # ... 更多场景
]
```

## 测试结果

### 输出文件
- **HTML报告**: `rag_test_report_场景名_时间戳.html`
- **CSV数据**: `rag_test_stats_场景名_时间戳.csv`
- **性能图表**: `rag_performance_charts_时间戳.png`
- **综合报告**: `rag_comprehensive_report_时间戳.csv`

### 关键指标解读

| 指标 | 优秀 | 良好 | 可接受 | 较差 |
|------|------|------|--------|------|
| 响应时间 | <2s | <5s | <10s | <20s |
| 首字节时间 | <0.5s | <1s | <2s | <5s |
| 成功率 | >99.5% | >99% | >95% | >90% |
| QPS | >10 | >5 | >1 | <1 |

## 测试建议

### 🔥 测试前准备
1. 确认接口连通性
2. 了解服务器资源情况
3. 选择合适的测试时间
4. 准备监控服务器性能

### 📈 测试策略
1. **从小到大**: 先小并发，逐步增加
2. **观察拐点**: 找到性能下降的临界点
3. **稳定性测试**: 在合理负载下长时间运行
4. **错误分析**: 重点关注失败请求的原因

### ⚠️ 注意事项
- 不要在生产环境进行大规模压力测试
- 测试时监控服务器CPU、内存、网络使用情况
- 极限测试可能对服务器造成压力，谨慎使用
- 建议在业务低峰期进行测试

## 故障排除

### 常见问题

**Q: 连接超时或连接被拒绝**
A: 检查网络连接和API地址是否正确

**Q: 认证失败**  
A: 检查API密钥是否正确和有效

**Q: 响应时间过长**
A: 可能是服务器负载过高，降低并发数重试

**Q: 成功率低**
A: 检查服务器状态和错误日志，可能需要调整测试参数

### 调试模式

在代码中设置调试标志:
```python
DEBUG = True  # 开启详细日志输出
```

## 扩展功能

### 自定义测试场景
可以在 `test_config.py` 中添加新的测试场景:

```python
{
    "name": "自定义场景",
    "users": 30,
    "spawn_rate": 5, 
    "run_time": 600,
    "enabled": True
}
```

### 添加新的测试问题
在配置文件中扩展问题库:

```python
"custom_questions": [
    "你的自定义问题1",
    "你的自定义问题2"
]
```

## 技术架构

- **测试框架**: Locust (Python)
- **数据分析**: NumPy, Pandas
- **图表生成**: Matplotlib, Seaborn  
- **进度显示**: tqdm
- **并发处理**: gevent

## 版本信息

- 版本: 1.0.0
- 更新时间: 2024-12-19
- 兼容性: Python 3.7+

## 联系支持

如有问题或建议，请联系开发团队。
