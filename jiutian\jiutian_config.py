#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
九天模型压力测试配置文件
"""

# 九天模型接口配置
JIUTIAN_CONFIG = {
    "api_url": "http://**************:8000/v1/chat/completions",
    "host": "http://**************:8000",
    "model_path": "/data1/JIUTIAN-75B-32k-chat/models/",
    "timeout": 300  # 请求超时时间(秒)
}

# 测试场景配置 - 针对16卡GPU
JIUTIAN_TEST_SCENARIOS = [
    {
        "name": "基准测试",
        "description": "基准负载性能测试",
        "users": 32,
        "spawn_rate": 8,
        "run_time": 100,
        "enabled": True
    },
    {
        "name": "轻负载测试",
        "description": "轻度负载下的性能表现",
        "users": 64,
        "spawn_rate": 16,
        "run_time": 300,
        "enabled": True
    },
    {
        "name": "中负载测试",
        "description": "中等负载下的性能表现",
        "users": 128,
        "spawn_rate": 32,
        "run_time": 500,
        "enabled": True
    },
    {
        "name": "高负载测试",
        "description": "高负载下的性能表现",
        "users": 200,
        "spawn_rate": 50,
        "run_time": 700,
        "enabled": True
    }
]

# 测试数据配置
JIUTIAN_TEST_DATA = {
    # 简单问题 - 基础对话和计算
    "simple_questions": [
        "你是谁？",
        "1+1等于几？",
        "今天天气怎么样？",
        "请说一句问候语",
        "什么是人工智能？",
        "Python是什么？",
        "请介绍一下自己",
        "现在几点了？",
        "你能做什么？",
        "谢谢你的帮助"
    ],
    
    # 中等复杂度问题 - 需要分析和推理
    "medium_questions": [
        "请分析一下当前AI技术的发展趋势",
        "如何解决气候变化问题？请提供具体建议",
        "编写一个Python函数计算斐波那契数列",
        "解释机器学习和深度学习的区别",
        "请推荐几本编程入门书籍并说明理由",
        "如何提高工作效率？给出5个建议",
        "分析电商行业的发展前景",
        "什么是区块链技术？有什么应用场景？"
    ],
    
    # 复杂问题 - 长文本和深度分析
    "complex_questions": [
        "请详细分析量子计算的工作原理，并说明其在密码学领域的应用前景",
        "设计一个完整的电商系统架构，包括前端、后端、数据库和缓存策略",
        "分析人工智能在医疗领域的应用现状和未来发展方向，包括机遇和挑战",
        "请详细解释深度学习中的注意力机制，并举例说明其在自然语言处理中的应用",
        "从技术、商业和社会角度全面分析自动驾驶汽车的发展现状和未来趋势"
    ],
    
    # 固定模型参数
    "model_params": {
        "model": "/data1/JIUTIAN-75B-32k-chat/models/",
        "max_tokens": 512,
        "presence_penalty": 1.03,
        "frequency_penalty": 1.0,
        "seed": None,
        "temperature": 0.5,
        "top_p": 0.95,
        "stream": False  # 非流式模式（API暂不支持流式）
    },
    
    # 任务权重配置 - 只使用简单问题为主
    "task_weights": {
        "simple": 3,    # 简单问题权重
        "medium": 2,    # 中等问题权重  
        "complex": 1    # 复杂问题权重
    }
}

# 性能指标阈值配置
JIUTIAN_PERFORMANCE_THRESHOLDS = {
    "response_time": {
        "excellent": 1.0,    # 优秀: <1秒
        "good": 3.0,         # 良好: <3秒
        "acceptable": 8.0,   # 可接受: <8秒
        "poor": 15.0         # 较差: <15秒
    },
    "first_byte_time": {
        "excellent": 0.3,    # 优秀: <0.3秒
        "good": 0.8,         # 良好: <0.8秒
        "acceptable": 2.0,   # 可接受: <2秒
        "poor": 5.0          # 较差: <5秒
    },
    "success_rate": {
        "excellent": 99.5,   # 优秀: >99.5%
        "good": 99.0,        # 良好: >99%
        "acceptable": 95.0,  # 可接受: >95%
        "poor": 90.0         # 较差: >90%
    },
    "qps": {
        "target_min": 5.0,      # 最低QPS目标
        "target_good": 20.0,    # 良好QPS目标
        "target_excellent": 50.0 # 优秀QPS目标
    }
}

def get_enabled_scenarios():
    """获取启用的测试场景"""
    return [scenario for scenario in JIUTIAN_TEST_SCENARIOS if scenario.get("enabled", True)]

def get_question_by_complexity(complexity="mixed"):
    """根据复杂度获取问题"""
    import random
    
    if complexity == "simple":
        return random.choice(JIUTIAN_TEST_DATA["simple_questions"])
    elif complexity == "medium":
        return random.choice(JIUTIAN_TEST_DATA["medium_questions"])
    elif complexity == "complex":
        return random.choice(JIUTIAN_TEST_DATA["complex_questions"])
    else:  # mixed
        all_questions = (
            JIUTIAN_TEST_DATA["simple_questions"] + 
            JIUTIAN_TEST_DATA["medium_questions"] + 
            JIUTIAN_TEST_DATA["complex_questions"]
        )
        return random.choice(all_questions)

def get_model_params():
    """获取模型参数"""
    return JIUTIAN_TEST_DATA["model_params"].copy()

def evaluate_performance(metric_name, value):
    """评估性能指标"""
    if metric_name not in JIUTIAN_PERFORMANCE_THRESHOLDS:
        return "unknown"
    
    thresholds = JIUTIAN_PERFORMANCE_THRESHOLDS[metric_name]
    
    if metric_name == "success_rate":
        # 成功率越高越好
        if value >= thresholds["excellent"]:
            return "excellent"
        elif value >= thresholds["good"]:
            return "good"
        elif value >= thresholds["acceptable"]:
            return "acceptable"
        elif value >= thresholds["poor"]:
            return "poor"
        else:
            return "critical"
    else:
        # 响应时间等越低越好
        if value <= thresholds["excellent"]:
            return "excellent"
        elif value <= thresholds["good"]:
            return "good"
        elif value <= thresholds["acceptable"]:
            return "acceptable"
        elif value <= thresholds["poor"]:
            return "poor"
        else:
            return "critical"

if __name__ == "__main__":
    # 配置文件测试
    print("九天模型压力测试配置")
    print("="*50)
    print(f"API地址: {JIUTIAN_CONFIG['api_url']}")
    print(f"模型路径: {JIUTIAN_CONFIG['model_path']}")
    print(f"启用的测试场景数: {len(get_enabled_scenarios())}")
    print(f"测试问题总数: {len(JIUTIAN_TEST_DATA['simple_questions']) + len(JIUTIAN_TEST_DATA['medium_questions']) + len(JIUTIAN_TEST_DATA['complex_questions'])}")
    
    # 示例使用
    print(f"\n示例问题: {get_question_by_complexity('simple')}")
    print(f"模型参数: {get_model_params()}")
    print(f"性能评估示例: 响应时间2秒 -> {evaluate_performance('response_time', 2.0)}")
