#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
九天模型压力测试启动脚本
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    """主函数"""
    try:
        from jiutian.jiutian_load_test import main as jiutian_main
        jiutian_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所需依赖包:")
        print("pip install locust numpy pandas matplotlib seaborn tqdm requests")
    except Exception as e:
        print(f"运行错误: {e}")

if __name__ == "__main__":
    main()
