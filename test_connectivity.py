#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG接口连通性测试脚本
在进行压力测试前，先验证接口是否正常工作
"""

import requests
import json
import time
from datetime import datetime

# 导入配置
try:
    from test_config import RAG_CONFIG, get_question_by_complexity, get_weighted_params
    API_URL = RAG_CONFIG["api_url"]
    API_KEY = RAG_CONFIG["api_key"]
    TIMEOUT = RAG_CONFIG.get("timeout", 30)
    print("✓ 使用配置文件中的设置")
except ImportError:
    # 使用默认配置
    API_URL = "http://122.14.231.235:3005/api/v1/chat/completions"
    API_KEY = "Bearer wisegpt-xUY1az1OrsRiNFbGYnbuOSPPTL6zXj5slj9Hx0ICG0QmD35b5n29M4CJ76bW"
    TIMEOUT = 30
    print("⚠ 使用默认配置")

def test_basic_connectivity():
    """测试基本连通性"""
    print("\n" + "="*60)
    print("1. 基本连通性测试")
    print("="*60)
    
    headers = {
        'Authorization': API_KEY,
        'Content-Type': 'application/json'
    }
    
    payload = {
        "chatId": "connectivity_test",
        "stream": False,
        "detail": False,
        "variables": {"uid": "test_user", "name": "RAG"},
        "messages": [{"content": "什么是支付机构？", "role": "user"}],
        "enable_thinking": False,
    }
    
    try:
        print(f"请求地址: {API_URL}")
        print(f"请求超时: {TIMEOUT}秒")
        
        start_time = time.time()
        response = requests.post(API_URL, headers=headers, json=payload, timeout=TIMEOUT)
        end_time = time.time()
        
        response_time = end_time - start_time
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应时间: {response_time:.3f}秒")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print("✓ JSON解析成功")
                
                # 检查响应结构
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0].get('message', {}).get('content', '')
                    print(f"✓ 响应内容长度: {len(content)}字符")
                    print(f"响应内容预览: {content[:200]}...")
                    return True
                else:
                    print("✗ 响应结构异常")
                    print(f"响应内容: {result}")
                    return False
                    
            except json.JSONDecodeError as e:
                print(f"✗ JSON解析失败: {str(e)}")
                print(f"响应内容: {response.text[:500]}")
                return False
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            print(f"错误内容: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"✗ 请求超时 (>{TIMEOUT}秒)")
        return False
    except requests.exceptions.ConnectionError:
        print("✗ 连接错误，请检查网络和服务器状态")
        return False
    except Exception as e:
        print(f"✗ 未知错误: {str(e)}")
        return False

def test_different_parameters():
    """测试不同参数组合"""
    print("\n" + "="*60)
    print("2. 参数组合测试")
    print("="*60)
    
    headers = {
        'Authorization': API_KEY,
        'Content-Type': 'application/json'
    }
    
    # 测试不同参数组合
    param_tests = [
        {"stream": False, "detail": False, "name": "非流式+简单"},
        {"stream": False, "detail": True, "name": "非流式+详细"},
        {"stream": True, "detail": False, "name": "流式+简单"},
        {"stream": True, "detail": True, "name": "流式+详细"}
    ]
    
    results = []
    
    for i, params in enumerate(param_tests, 1):
        print(f"\n测试 {i}/4: {params['name']}")
        
        payload = {
            "chatId": f"param_test_{i}",
            "stream": params["stream"],
            "detail": params["detail"],
            "variables": {"uid": f"test_user_{i}", "name": "ParamTest"},
            "messages": [{"content": "支付业务许可证是什么？", "role": "user"}]
        }
        
        try:
            start_time = time.time()
            response = requests.post(API_URL, headers=headers, json=payload, timeout=TIMEOUT)
            end_time = time.time()
            
            response_time = end_time - start_time
            success = response.status_code == 200
            
            result = {
                "params": params['name'],
                "success": success,
                "response_time": response_time,
                "status_code": response.status_code
            }
            
            if success:
                try:
                    data = response.json()
                    content_length = len(str(data))
                    result["content_length"] = content_length
                    print(f"  ✓ 成功 - 响应时间: {response_time:.3f}s, 内容长度: {content_length}")
                except:
                    print(f"  ⚠ 响应成功但JSON解析失败")
            else:
                print(f"  ✗ 失败 - 状态码: {response.status_code}")
                
            results.append(result)
            
        except Exception as e:
            print(f"  ✗ 异常: {str(e)}")
            results.append({
                "params": params['name'],
                "success": False,
                "error": str(e)
            })
    
    # 汇总结果
    print(f"\n参数测试汇总:")
    success_count = sum(1 for r in results if r.get("success", False))
    print(f"成功: {success_count}/{len(results)}")
    
    return results

def test_performance_baseline():
    """测试性能基线"""
    print("\n" + "="*60)
    print("3. 性能基线测试")
    print("="*60)
    
    headers = {
        'Authorization': API_KEY,
        'Content-Type': 'application/json'
    }
    
    # 连续发送5个请求测试性能
    test_questions = [
        "什么是支付机构？",
        "支付业务许可证是什么？",
        "网络支付包括哪些类型？",
        "预付卡是什么？",
        "银行卡收单业务是什么？"
    ]
    
    response_times = []
    success_count = 0
    
    print(f"发送 {len(test_questions)} 个连续请求...")
    
    for i, question in enumerate(test_questions, 1):
        payload = {
            "chatId": f"baseline_test_{i}",
            "stream": False,
            "detail": False,
            "variables": {"uid": f"baseline_user_{i}", "name": "BaselineTest"},
            "messages": [{"content": question, "role": "user"}]
        }
        
        try:
            start_time = time.time()
            response = requests.post(API_URL, headers=headers, json=payload, timeout=TIMEOUT)
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            
            if response.status_code == 200:
                success_count += 1
                print(f"  请求 {i}: ✓ {response_time:.3f}s")
            else:
                print(f"  请求 {i}: ✗ {response.status_code} - {response_time:.3f}s")
                
        except Exception as e:
            print(f"  请求 {i}: ✗ 异常 - {str(e)}")
    
    # 计算统计信息
    if response_times:
        import statistics
        avg_time = statistics.mean(response_times)
        min_time = min(response_times)
        max_time = max(response_times)
        
        print(f"\n性能基线统计:")
        print(f"成功率: {success_count}/{len(test_questions)} ({success_count/len(test_questions)*100:.1f}%)")
        print(f"平均响应时间: {avg_time:.3f}秒")
        print(f"最快响应时间: {min_time:.3f}秒")
        print(f"最慢响应时间: {max_time:.3f}秒")
        
        return {
            "success_rate": success_count/len(test_questions)*100,
            "avg_response_time": avg_time,
            "min_response_time": min_time,
            "max_response_time": max_time
        }
    
    return None

def main():
    """主函数"""
    print("RAG接口连通性和基础性能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行测试
    connectivity_ok = test_basic_connectivity()
    
    if connectivity_ok:
        param_results = test_different_parameters()
        baseline_results = test_performance_baseline()
        
        print("\n" + "="*60)
        print("测试总结")
        print("="*60)
        print("✓ 基本连通性: 正常")
        
        param_success = sum(1 for r in param_results if r.get("success", False))
        print(f"✓ 参数兼容性: {param_success}/4 组合正常")
        
        if baseline_results:
            print(f"✓ 性能基线: 平均 {baseline_results['avg_response_time']:.3f}s")
            
            # 性能评估
            avg_time = baseline_results['avg_response_time']
            if avg_time < 2:
                performance_level = "优秀"
            elif avg_time < 5:
                performance_level = "良好"
            elif avg_time < 10:
                performance_level = "可接受"
            else:
                performance_level = "需要优化"
            
            print(f"✓ 性能评估: {performance_level}")
        
        print("\n🚀 接口状态良好，可以进行压力测试")
        print("运行命令: python run_rag_test.py")
        
    else:
        print("\n❌ 接口连通性测试失败")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. API地址是否正确")
        print("3. API密钥是否有效")
        print("4. 服务器是否正常运行")

if __name__ == "__main__":
    main()
