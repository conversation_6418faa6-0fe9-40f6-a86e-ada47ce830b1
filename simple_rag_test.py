#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的RAG接口压力测试脚本
- 只运行前4个测试场景
- 只问简单问题
- 只使用 stream=True, detail=False 配置
"""

import json
import time
import random
import threading
from datetime import datetime
import numpy as np
import pandas as pd
from tqdm import tqdm

# Locust相关导入
from locust import HttpUser, task, between
from locust.env import Environment

# 导入配置
from test_config import RAG_CONFIG, TEST_SCENARIOS, TEST_DATA_CONFIG

# 配置信息
RAG_POST_API_URL = RAG_CONFIG["api_url"]
RAG_POST_API_KEY = RAG_CONFIG["api_key"]
RAG_HOST = RAG_CONFIG["host"]

# 简化的测试数据 - 只使用简单问题
SIMPLE_QUESTIONS = TEST_DATA_CONFIG["simple_questions"]

# 固定参数配置 - 只使用 stream=True, detail=False
FIXED_PARAMS = {"stream": True, "detail": False}

# 只使用前4个测试场景
SIMPLE_TEST_SCENARIOS = TEST_SCENARIOS[:4]

print(f"将运行 {len(SIMPLE_TEST_SCENARIOS)} 个测试场景:")
for i, scenario in enumerate(SIMPLE_TEST_SCENARIOS, 1):
    print(f"  {i}. {scenario['name']} - {scenario['users']}用户, {scenario['run_time']}秒")
print(f"使用 {len(SIMPLE_QUESTIONS)} 个简单问题")
print(f"固定参数: {FIXED_PARAMS}")


class SimplePerformanceMetrics:
    """简化的性能指标收集器"""
    
    def __init__(self):
        self.response_times = []
        self.first_byte_times = []
        self.success_count = 0
        self.failure_count = 0
        self.start_time = None
        self.end_time = None
        self.concurrent_users = 0
        self.detailed_results = []
        self.lock = threading.Lock()
    
    def add_result(self, response_time, first_byte_time, success, concurrent_users):
        """添加测试结果"""
        with self.lock:
            self.response_times.append(response_time)
            if first_byte_time:
                self.first_byte_times.append(first_byte_time)
            
            if success:
                self.success_count += 1
            else:
                self.failure_count += 1
            
            self.concurrent_users = max(self.concurrent_users, concurrent_users)
            
            self.detailed_results.append({
                '时间戳': datetime.now(),
                '响应时间': response_time,
                '首字节时间': first_byte_time,
                '是否成功': success,
                '并发用户数': concurrent_users
            })
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.response_times:
            return {}
        
        total_requests = self.success_count + self.failure_count
        duration = (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0
        
        stats = {
            '总请求数': total_requests,
            '成功请求数': self.success_count,
            '失败请求数': self.failure_count,
            '成功率(%)': (self.success_count / total_requests * 100) if total_requests > 0 else 0,
            '平均响应时间(秒)': np.mean(self.response_times),
            '最小响应时间(秒)': np.min(self.response_times),
            '最大响应时间(秒)': np.max(self.response_times),
            'P50响应时间(秒)': np.percentile(self.response_times, 50),
            'P95响应时间(秒)': np.percentile(self.response_times, 95),
            'P99响应时间(秒)': np.percentile(self.response_times, 99),
            'QPS': total_requests / duration if duration > 0 else 0,
            '最大并发用户数': self.concurrent_users,
            '测试持续时间(秒)': duration
        }
        
        if self.first_byte_times:
            stats.update({
                '平均首字节时间(秒)': np.mean(self.first_byte_times),
                '最小首字节时间(秒)': np.min(self.first_byte_times),
                '最大首字节时间(秒)': np.max(self.first_byte_times),
                'P95首字节时间(秒)': np.percentile(self.first_byte_times, 95)
            })
        
        return stats
    



# 全局性能指标对象
performance_metrics = SimplePerformanceMetrics()


class SimpleRAGUser(HttpUser):
    """简化的RAG压力测试用户类"""
    
    host = RAG_HOST
    wait_time = between(1, 3)  # 请求间隔时间
    
    def on_start(self):
        """用户开始时的初始化"""
        self.client.headers.update({
            'Authorization': RAG_POST_API_KEY,
            'Content-Type': 'application/json'
        })
    
    def make_request(self, question):
        """发送RAG请求并收集性能指标"""
        payload = {
            "chatId": f"simple_test_{self.environment.runner.user_count}_{random.randint(1000, 9999)}",
            "stream": FIXED_PARAMS["stream"],
            "detail": FIXED_PARAMS["detail"],
            "variables": {"uid": f"test_user_{random.randint(1000, 9999)}", "name": "SimpleRAG_LoadTest"},
            "messages": [{"content": question, "role": "user"}]
        }
        
        start_time = time.time()
        first_byte_time = None
        success = False
        
        try:
            with self.client.post(
                "/api/v1/chat/completions",
                json=payload,
                catch_response=True,
                stream=FIXED_PARAMS["stream"]
            ) as response:
                
                # 记录首字节时间
                if not first_byte_time:
                    first_byte_time = time.time() - start_time
                
                if response.status_code == 200:
                    success = True
                    # 处理流式响应
                    if FIXED_PARAMS["stream"]:
                        content_length = 0
                        for chunk in response.iter_content(chunk_size=1024):
                            if chunk:
                                content_length += len(chunk)
                        response.success()
                    else:
                        # 非流式响应
                        try:
                            result = response.json()
                            if 'choices' in result and len(result['choices']) > 0:
                                response.success()
                            else:
                                response.failure("Empty or invalid response")
                                success = False
                        except json.JSONDecodeError:
                            response.failure("Invalid JSON response")
                            success = False
                else:
                    response.failure(f"HTTP {response.status_code}")
                    success = False
                    
        except Exception as e:
            success = False
            print(f"请求异常: {str(e)}")
            
        # 记录性能指标
        end_time = time.time()
        response_time = end_time - start_time
        
        performance_metrics.add_result(
            response_time=response_time,
            first_byte_time=first_byte_time,
            success=success,
            concurrent_users=self.environment.runner.user_count if hasattr(self.environment, 'runner') else 1
        )
    
    @task(1)
    def simple_question_task(self):
        """简单问题测试任务 - 只使用简单问题"""
        question = random.choice(SIMPLE_QUESTIONS)
        self.make_request(question)


class SimpleLoadTestRunner:
    """简化的压力测试执行器"""

    def __init__(self):
        self.results = {}
        # 生成统一的CSV文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.csv_filename = f"简化RAG压力测试报告_{timestamp}.csv"
        self.scenario_count = 0

    def run_test_scenario(self, users, spawn_rate, run_time, scenario_name):
        """运行单个测试场景"""
        print(f"\n{'='*60}")
        print(f"开始执行测试场景: {scenario_name}")
        print(f"并发用户数: {users}, 启动速率: {spawn_rate}/秒, 运行时间: {run_time}秒")
        print(f"参数配置: {FIXED_PARAMS}")
        print(f"{'='*60}")

        # 重置性能指标
        global performance_metrics
        performance_metrics = SimplePerformanceMetrics()
        performance_metrics.start_time = datetime.now()

        # 设置Locust环境
        env = Environment(user_classes=[SimpleRAGUser])
        env.create_local_runner()

        # 开始测试
        env.runner.start(users, spawn_rate=spawn_rate)

        # 运行指定时间
        start_time = time.time()
        with tqdm(total=run_time, desc=f"执行{scenario_name}") as pbar:
            while time.time() - start_time < run_time:
                time.sleep(1)
                pbar.update(1)

                # 实时显示统计信息
                if int(time.time() - start_time) % 10 == 0:  # 每10秒显示一次
                    current_stats = performance_metrics.get_statistics()
                    if current_stats:
                        pbar.set_postfix({
                            'QPS': f"{current_stats.get('QPS', 0):.1f}",
                            '平均响应时间': f"{current_stats.get('平均响应时间(秒)', 0):.2f}s",
                            '成功率': f"{current_stats.get('成功率(%)', 0):.1f}%"
                        })

        # 停止测试
        env.runner.stop()
        performance_metrics.end_time = datetime.now()

        # 收集结果
        stats = performance_metrics.get_statistics()
        self.results[scenario_name] = stats

        # 显示结果
        self.print_scenario_results(scenario_name, stats)

        # 保存详细数据到统一的CSV文件中
        self.append_scenario_data_to_csv(scenario_name, performance_metrics)

        return stats

    def append_scenario_data_to_csv(self, scenario_name, metrics):
        """将场景数据追加到统一的CSV文件中"""
        self.scenario_count += 1

        # 为每条详细记录添加场景名称
        detailed_data = []
        for record in metrics.detailed_results:
            record_with_scenario = record.copy()
            record_with_scenario['测试场景'] = scenario_name
            record_with_scenario['场景序号'] = self.scenario_count
            detailed_data.append(record_with_scenario)

        # 转换为DataFrame
        df = pd.DataFrame(detailed_data)

        # 重新排列列的顺序，把场景信息放在前面
        if not df.empty:
            cols = ['场景序号', '测试场景', '时间戳', '并发用户数', '响应时间', '首字节时间', '是否成功']
            df = df[cols]

        # 追加到CSV文件
        if self.scenario_count == 1:
            # 第一个场景，创建新文件并写入表头
            df.to_csv(self.csv_filename, index=False, encoding='utf-8-sig', mode='w')
            print(f"创建测试报告文件: {self.csv_filename}")
        else:
            # 后续场景，追加数据（不写表头）
            df.to_csv(self.csv_filename, index=False, encoding='utf-8-sig', mode='a', header=False)
            print(f"场景数据已追加到: {self.csv_filename}")

    def print_scenario_results(self, scenario_name, stats):
        """打印场景测试结果"""
        print(f"\n{'-'*50}")
        print(f"测试场景: {scenario_name} - 结果汇总")
        print(f"{'-'*50}")
        print(f"总请求数: {stats.get('总请求数', 0)}")
        print(f"成功请求数: {stats.get('成功请求数', 0)}")
        print(f"失败请求数: {stats.get('失败请求数', 0)}")
        print(f"成功率: {stats.get('成功率(%)', 0):.2f}%")
        print(f"QPS: {stats.get('QPS', 0):.2f}")
        print(f"最大并发用户数: {stats.get('最大并发用户数', 0)}")
        print(f"测试持续时间: {stats.get('测试持续时间(秒)', 0):.2f}秒")
        print(f"\n响应时间统计:")
        print(f"  平均响应时间: {stats.get('平均响应时间(秒)', 0):.3f}秒")
        print(f"  最小响应时间: {stats.get('最小响应时间(秒)', 0):.3f}秒")
        print(f"  最大响应时间: {stats.get('最大响应时间(秒)', 0):.3f}秒")
        print(f"  P50响应时间: {stats.get('P50响应时间(秒)', 0):.3f}秒")
        print(f"  P95响应时间: {stats.get('P95响应时间(秒)', 0):.3f}秒")
        print(f"  P99响应时间: {stats.get('P99响应时间(秒)', 0):.3f}秒")

        if '平均首字节时间(秒)' in stats:
            print(f"\n首字节时间统计:")
            print(f"  平均首字节时间: {stats.get('平均首字节时间(秒)', 0):.3f}秒")
            print(f"  最小首字节时间: {stats.get('最小首字节时间(秒)', 0):.3f}秒")
            print(f"  最大首字节时间: {stats.get('最大首字节时间(秒)', 0):.3f}秒")
            print(f"  P95首字节时间: {stats.get('P95首字节时间(秒)', 0):.3f}秒")

    def run_simple_comprehensive_test(self):
        """运行简化的综合压力测试"""
        print("开始简化RAG接口压力测试")
        print("="*80)
        print(f"测试配置: 只使用简单问题, 参数固定为 {FIXED_PARAMS}")
        print("="*80)

        # 执行前4个测试场景
        for scenario in SIMPLE_TEST_SCENARIOS:
            try:
                self.run_test_scenario(
                    users=scenario["users"],
                    spawn_rate=scenario["spawn_rate"],
                    run_time=scenario["run_time"],
                    scenario_name=scenario["name"]
                )
                # 场景间休息时间
                print(f"场景完成，休息30秒...")
                time.sleep(30)
            except Exception as e:
                print(f"测试场景 {scenario['name']} 执行失败: {str(e)}")
                continue

        # 生成综合报告
        self.generate_simple_report()

    def generate_simple_report(self):
        """生成简化的综合测试报告"""
        if not self.results:
            print("没有测试结果可生成报告")
            return

        print(f"\n{'='*80}")
        print("简化RAG接口压力测试 - 综合报告")
        print(f"{'='*80}")

        # 创建结果对比表
        df_results = pd.DataFrame(self.results).T

        # 显示关键指标对比
        key_metrics = ['最大并发用户数', 'QPS', '平均响应时间(秒)', '成功率(%)', 'P95响应时间(秒)']
        if not df_results.empty:
            print("\n关键性能指标对比:")
            print("-" * 80)
            for metric in key_metrics:
                if metric in df_results.columns:
                    print(f"{metric:20s}: {df_results[metric].to_dict()}")

        # 保存综合报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"简化RAG综合性能报告_{timestamp}.csv"
        df_results.to_csv(report_filename, encoding='utf-8-sig')
        print(f"\n综合报告已保存到: {report_filename}")
        print(f"详细测试数据已保存到: {self.csv_filename}")


def main():
    """主函数"""
    print("简化RAG接口压力测试工具")
    print("="*50)
    print(f"配置: 只使用简单问题, 参数固定为 {FIXED_PARAMS}")
    print(f"将运行前4个测试场景: {[s['name'] for s in SIMPLE_TEST_SCENARIOS]}")

    # 首先测试接口连通性
    print("\n测试接口连通性...")
    try:
        import requests
        headers = {'Authorization': RAG_POST_API_KEY, 'Content-Type': 'application/json'}
        payload = {
            "chatId": "connectivity_test",
            "stream": FIXED_PARAMS["stream"],
            "detail": FIXED_PARAMS["detail"],
            "variables": {"uid": "test_user", "name": "ConnectivityTest"},
            "messages": [{"content": "什么是支付机构？", "role": "user"}]
        }
        response = requests.post(RAG_POST_API_URL, headers=headers, json=payload, timeout=120)
        if response.status_code == 200:
            print("✓ 接口连通性测试通过")
        else:
            print(f"✗ 接口连通性测试失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"✗ 接口连通性测试失败: {str(e)}")
        return

    # 创建测试执行器并运行简化测试
    runner = SimpleLoadTestRunner()
    
    print("\n开始执行简化压力测试...")
    runner.run_simple_comprehensive_test()
    
    print("\n✓ 简化压力测试完成!")


if __name__ == "__main__":
    main()
