#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG压力测试快速运行脚本
简化版本，用于快速执行基本的压力测试
"""

import sys
import os
import subprocess
import time
from datetime import datetime

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        'locust',
        'numpy',
        'pandas', 
        'matplotlib',
        'seaborn',
        'tqdm',
        'requests'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def run_locust_web_ui():
    """启动Locust Web界面"""
    print("启动Locust Web界面...")
    print("Web界面地址: http://localhost:8089")
    print("按 Ctrl+C 停止测试")
    
    try:
        cmd = [
            sys.executable, "-m", "locust",
            "-f", "rag_load_test.py",
            "--host", "http://**************:3005",
            "--web-host", "0.0.0.0",
            "--web-port", "8089"
        ]
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n测试已停止")

def run_headless_test():
    """运行无界面测试"""
    print("运行无界面压力测试...")
    
    # 测试配置
    configs = [
        {"users": 5, "spawn_rate": 1, "time": "2m", "name": "轻负载测试"},
        {"users": 10, "spawn_rate": 2, "time": "3m", "name": "中等负载测试"},
        {"users": 20, "spawn_rate": 4, "time": "3m", "name": "高负载测试"},
    ]
    
    for config in configs:
        print(f"\n{'='*60}")
        print(f"执行: {config['name']}")
        print(f"并发用户: {config['users']}, 启动速率: {config['spawn_rate']}/s, 时长: {config['time']}")
        print(f"{'='*60}")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_report = f"rag_test_report_{config['name']}_{timestamp}.html"
        csv_report = f"rag_test_stats_{config['name']}_{timestamp}.csv"
        
        try:
            cmd = [
                sys.executable, "-m", "locust",
                "-f", "rag_load_test.py",
                "--host", "http://**************:3005",
                "--headless",
                "-u", str(config['users']),
                "-r", str(config['spawn_rate']),
                "-t", config['time'],
                "--html", html_report,
                "--csv", csv_report.replace('.csv', '')
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"✓ {config['name']} 完成")
                print(f"  HTML报告: {html_report}")
                print(f"  CSV数据: {csv_report}")
            else:
                print(f"✗ {config['name']} 失败")
                print(f"错误信息: {result.stderr}")
                
        except Exception as e:
            print(f"✗ 执行 {config['name']} 时出错: {str(e)}")
        
        # 测试间隔
        if config != configs[-1]:  # 不是最后一个测试
            print("等待30秒后继续下一个测试...")
            time.sleep(30)

def run_single_test():
    """运行单个自定义测试"""
    print("自定义单个测试")
    
    try:
        users = int(input("并发用户数 (默认10): ") or "10")
        spawn_rate = int(input("启动速率/秒 (默认2): ") or "2")
        duration = input("测试时长 (默认2m): ") or "2m"
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        html_report = f"rag_custom_test_{timestamp}.html"
        csv_report = f"rag_custom_stats_{timestamp}.csv"
        
        print(f"\n开始测试: {users}用户, {spawn_rate}/s启动速率, 持续{duration}")
        
        cmd = [
            sys.executable, "-m", "locust",
            "-f", "rag_load_test.py",
            "--host", "http://**************:3005",
            "--headless",
            "-u", str(users),
            "-r", str(spawn_rate),
            "-t", duration,
            "--html", html_report,
            "--csv", csv_report.replace('.csv', '')
        ]
        
        result = subprocess.run(cmd)
        
        if result.returncode == 0:
            print(f"✓ 测试完成")
            print(f"  HTML报告: {html_report}")
            print(f"  CSV数据: {csv_report}")
        else:
            print("✗ 测试失败")
            
    except ValueError:
        print("输入参数无效")
    except KeyboardInterrupt:
        print("\n测试被用户中断")

def main():
    """主函数"""
    print("RAG接口压力测试工具")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 检查测试文件
    if not os.path.exists("rag_load_test.py"):
        print("错误: 找不到 rag_load_test.py 文件")
        print("请确保该文件在当前目录中")
        return
    
    print("\n选择测试模式:")
    print("1. Web界面模式 (推荐) - 可视化界面，实时监控")
    print("2. 无界面批量测试 - 自动执行多个测试场景")
    print("3. 单个自定义测试 - 快速执行单个测试")
    print("4. 使用Python脚本直接运行")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == "1":
        run_locust_web_ui()
    elif choice == "2":
        run_headless_test()
    elif choice == "3":
        run_single_test()
    elif choice == "4":
        print("运行Python脚本...")
        try:
            subprocess.run([sys.executable, "rag_load_test.py"])
        except KeyboardInterrupt:
            print("\n测试被用户中断")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
