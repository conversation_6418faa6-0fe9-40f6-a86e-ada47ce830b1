from locust import HttpUser, task
import logging
import os

class ModelUser(HttpUser):
    @task
    def generate_text(self):
        headers = {
            'Content-Type': 'application/json'
        }
        payload = {
            "model": "/data/models/Qwen3-32B",
            "messages": [
            {
                "role": "assistant",
                "content": "You are a assistant."
            },
            {
                "role": "user",
                "content": "你是谁？/no_think "
            }
        ],
            "temperature": 0.7,
            "prompt": "你是谁？/no_think",
            "stream": False
        }
        console_logger = logging.getLogger("locust.stats_logger")

        with self.client.post("/v1/chat/completions", json=payload, headers=headers, catch_response=True) as response:
            try:
                result = response.json()
                input_tokens = len(payload["prompt"].split())  # 简单估算
                output_tokens = len(result["choices"][0]["message"]["content"].split())
                latency = response.elapsed.total_seconds() * 1000  # ms
                tps = output_tokens / (latency / 1000) if latency > 0 else 0

                # 自定义指标上报
                console_logger.info(f"Input Tokens: {input_tokens}, Output Tokens: {output_tokens}, TPS: {tps:.2f}")
            except Exception as e:
                response.failure("Parsing response failed: %s" % e)
    
    # @task
    # def langflow_api(self):
    #     # 从环境变量获取API密钥
    #     api_key = os.environ.get("LANGFLOW_API_KEY", "your_default_api_key")
        
    #     headers = {
    #         'Content-Type': 'application/json',
    #         'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI0ZmRjMmNkNy05MGU0LTRkYWUtOTk5ZC1lNDJhYjk0ZGVjMzIiLCJ0eXBlIjoiYWNjZXNzIiwiZXhwIjoxNzUxMzU2MTgwfQ.w_9PAp4FTkyYOgKTvg-ItOApXrHMx_ulJVMe0MF4-E0'
    #     }
        
    #     payload = {
    #         "input_value": "你是谁？/no_think",
    #         "output_type": "chat",
    #         "input_type": "chat"
    #     }
        
    #     console_logger = logging.getLogger("locust.stats_logger")
        
    #     with self.client.post("/api/v1/run/24e78a3d-3937-424f-8cd9-70f05673885b", 
    #                          json=payload, 
    #                          headers=headers, 
    #                          params={"stream": "false"}, 
    #                          catch_response=True) as response:
    #         try:
    #             result = response.json()
    #             latency = response.elapsed.total_seconds() * 1000  # ms
                
    #             # 记录响应信息
    #             console_logger.info(f"Langflow API - Status: {response.status_code}, Latency: {latency:.2f}ms")
                
    #             # 可以根据实际响应结构添加更多指标
    #             if response.status_code != 200:
    #                 response.failure(f"Failed with status code: {response.status_code}")
    #         except Exception as e:
    #             response.failure(f"Parsing response failed: {e}")
