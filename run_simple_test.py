#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行简化RAG压力测试的脚本
直接调用，无需额外配置
"""

import subprocess
import sys
import os

def run_simple_test():
    """运行简化的压力测试"""
    print("="*60)
    print("快速启动简化RAG压力测试")
    print("="*60)
    print("测试配置:")
    print("- 只运行前4个测试场景")
    print("- 只使用简单问题")
    print("- 固定参数: stream=True, detail=False")
    print("="*60)
    
    try:
        # 直接运行简化测试脚本
        print("启动测试...")
        result = subprocess.run([
            sys.executable, "simple_rag_test.py"
        ], cwd=os.getcwd(), capture_output=False)
        
        if result.returncode == 0:
            print("\n✓ 测试完成!")
        else:
            print(f"\n✗ 测试失败，返回码: {result.returncode}")
            
    except Exception as e:
        print(f"✗ 运行测试时出错: {str(e)}")

def run_locust_web():
    """使用Locust Web界面运行测试"""
    print("="*60)
    print("启动Locust Web界面")
    print("="*60)
    print("将打开 http://localhost:8089")
    print("在Web界面中可以手动控制测试参数")
    print("="*60)
    
    try:
        # 使用简化的测试脚本启动Locust Web界面
        print("启动Locust Web界面...")
        subprocess.run([
            sys.executable, "-m", "locust", 
            "-f", "simple_rag_test.py",
            "--host", "http://**************:3005"
        ], cwd=os.getcwd())
        
    except KeyboardInterrupt:
        print("\n用户中断测试")
    except Exception as e:
        print(f"✗ 启动Locust时出错: {str(e)}")

def main():
    """主函数"""
    print("RAG压力测试快速启动工具")
    print("="*40)
    print("选择运行模式:")
    print("1. 自动运行简化测试 (推荐)")
    print("2. 启动Locust Web界面 (手动控制)")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            run_simple_test()
            break
        elif choice == "2":
            run_locust_web()
            break
        elif choice == "3":
            print("退出")
            break
        else:
            print("无效选择，请输入 1、2 或 3")

if __name__ == "__main__":
    main()
