#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
九天模型压力测试脚本
- 测试16卡GPU配置下的模型性能
- 关注最大并发数、最佳并发数、首字延迟、吞吐量等指标
- 生成中文性能报告
"""

import json
import time
import random
import threading
import re
from datetime import datetime
import numpy as np
import pandas as pd
from tqdm import tqdm

# Locust相关导入
from locust import HttpUser, task, between
from locust.env import Environment

# 导入配置
from .jiutian_config import (
    JIUTIAN_CONFIG, JIUTIAN_TEST_SCENARIOS, JIUTIAN_TEST_DATA,
    get_enabled_scenarios, get_question_by_complexity, get_model_params
)

# 配置信息
JIUTIAN_API_URL = JIUTIAN_CONFIG["api_url"]
JIUTIAN_HOST = JIUTIAN_CONFIG["host"]
MODEL_PARAMS = get_model_params()

# 测试数据
SIMPLE_QUESTIONS = JIUTIAN_TEST_DATA["simple_questions"]
MEDIUM_QUESTIONS = JIUTIAN_TEST_DATA["medium_questions"]
COMPLEX_QUESTIONS = JIUTIAN_TEST_DATA["complex_questions"]

# Token处理函数
def extract_tokens_from_response(response_json):
    """从API响应中提取token统计信息"""
    try:
        if 'usage' in response_json:
            return {
                'prompt_tokens': response_json['usage'].get('prompt_tokens', 0),
                'completion_tokens': response_json['usage'].get('completion_tokens', 0),
                'total_tokens': response_json['usage'].get('total_tokens', 0)
            }
    except:
        pass
    return None

def estimate_tokens(text):
    """估算文本的token数量"""
    if not text:
        return 0
    # 中文字符约1个字符=1个token，英文约4个字符=1个token
    chinese_chars = len(re.findall(r'[\u4e00-\u9fff]', text))
    english_chars = len(re.findall(r'[a-zA-Z]', text))
    other_chars = len(text) - chinese_chars - english_chars
    return chinese_chars + english_chars // 4 + other_chars // 2

def extract_response_content(response_json):
    """从响应中提取生成的文本内容"""
    try:
        if 'choices' in response_json and len(response_json['choices']) > 0:
            choice = response_json['choices'][0]
            if 'message' in choice and 'content' in choice['message']:
                return choice['message']['content']
            elif 'text' in choice:
                return choice['text']
    except:
        pass
    return ""

print(f"九天模型压力测试初始化")
print(f"API地址: {JIUTIAN_API_URL}")
print(f"模型路径: {MODEL_PARAMS['model']}")
print(f"将运行 {len(JIUTIAN_TEST_SCENARIOS)} 个测试场景")
print(f"使用 {len(SIMPLE_QUESTIONS + MEDIUM_QUESTIONS + COMPLEX_QUESTIONS)} 个测试问题")
print(f"模型参数: temperature={MODEL_PARAMS['temperature']}, max_tokens={MODEL_PARAMS['max_tokens']}")


class JiutianPerformanceMetrics:
    """九天模型性能指标收集器"""

    def __init__(self):
        self.response_times = []
        self.first_byte_times = []
        self.success_count = 0
        self.failure_count = 0
        self.start_time = None
        self.end_time = None
        self.concurrent_users = 0
        self.detailed_results = []
        # Token统计相关字段
        self.total_input_tokens = 0
        self.total_output_tokens = 0
        self.total_tokens = 0
        self.token_details = []
        self.lock = threading.Lock()
    
    def add_result(self, response_time, first_byte_time, success, concurrent_users, question_type, token_info=None):
        """添加测试结果"""
        with self.lock:
            self.response_times.append(response_time)
            if first_byte_time:
                self.first_byte_times.append(first_byte_time)

            if success:
                self.success_count += 1
            else:
                self.failure_count += 1

            self.concurrent_users = max(self.concurrent_users, concurrent_users)

            # 处理Token统计
            input_tokens = 0
            output_tokens = 0
            total_tokens = 0

            if token_info:
                input_tokens = token_info.get('prompt_tokens', 0)
                output_tokens = token_info.get('completion_tokens', 0)
                total_tokens = token_info.get('total_tokens', 0)

                self.total_input_tokens += input_tokens
                self.total_output_tokens += output_tokens
                self.total_tokens += total_tokens

                self.token_details.append({
                    'timestamp': datetime.now(),
                    'input_tokens': input_tokens,
                    'output_tokens': output_tokens,
                    'total_tokens': total_tokens,
                    'success': success
                })

            self.detailed_results.append({
                '时间戳': datetime.now(),
                '响应时间': response_time,
                '首字节时间': first_byte_time,
                '是否成功': success,
                '并发用户数': concurrent_users,
                '问题类型': question_type,
                '输入tokens': input_tokens,
                '输出tokens': output_tokens,
                '总tokens': total_tokens
            })
    
    def get_statistics(self):
        """获取统计信息"""
        if not self.response_times:
            return {}
        
        total_requests = self.success_count + self.failure_count
        duration = (self.end_time - self.start_time).total_seconds() if self.start_time and self.end_time else 0
        
        # 计算Token吞吐量相关指标
        token_throughput = self.total_output_tokens / duration if duration > 0 else 0
        avg_tokens_per_request = self.total_output_tokens / self.success_count if self.success_count > 0 else 0
        gpu_count = 16  # 16卡GPU
        token_throughput_per_gpu = token_throughput / gpu_count if gpu_count > 0 else 0

        stats = {
            '总请求数': total_requests,
            '成功请求数': self.success_count,
            '失败请求数': self.failure_count,
            '成功率(%)': (self.success_count / total_requests * 100) if total_requests > 0 else 0,
            '平均响应时间(秒)': np.mean(self.response_times),
            '最小响应时间(秒)': np.min(self.response_times),
            '最大响应时间(秒)': np.max(self.response_times),
            'P50响应时间(秒)': np.percentile(self.response_times, 50),
            'P95响应时间(秒)': np.percentile(self.response_times, 95),
            'P99响应时间(秒)': np.percentile(self.response_times, 99),
            'QPS': total_requests / duration if duration > 0 else 0,
            '最大并发用户数': self.concurrent_users,
            '测试持续时间(秒)': duration,
            # Token相关指标
            '总输入tokens': self.total_input_tokens,
            '总输出tokens': self.total_output_tokens,
            '总tokens': self.total_tokens,
            '平均每请求输出tokens': avg_tokens_per_request,
            'Token吞吐量(tokens/s)': token_throughput,
            '单卡Token吞吐量(tokens/s)': token_throughput_per_gpu,
            'Token效率(tokens/s/用户)': token_throughput / self.concurrent_users if self.concurrent_users > 0 else 0
        }
        
        if self.first_byte_times:
            stats.update({
                '平均首字节时间(秒)': np.mean(self.first_byte_times),
                '最小首字节时间(秒)': np.min(self.first_byte_times),
                '最大首字节时间(秒)': np.max(self.first_byte_times),
                'P95首字节时间(秒)': np.percentile(self.first_byte_times, 95)
            })
        
        return stats


# 全局性能指标对象
performance_metrics = JiutianPerformanceMetrics()


class JiutianUser(HttpUser):
    """九天模型压力测试用户类"""
    
    host = JIUTIAN_HOST
    wait_time = between(1, 3)  # 请求间隔时间
    
    def on_start(self):
        """用户开始时的初始化"""
        self.client.headers.update({
            'Content-Type': 'application/json'
        })
        self.question_counts = {'simple': 0, 'medium': 0, 'complex': 0}
    
    def make_request(self, question, question_type):
        """发送九天模型请求并收集性能指标"""
        payload = {
            **MODEL_PARAMS,
            "messages": [
                {
                    "role": "assistant",
                    "content": "You are a helpful AI assistant."
                },
                {
                    "role": "user", 
                    "content": question
                }
            ]
        }
        
        start_time = time.time()
        first_byte_time = None
        success = False
        token_info = None
        result = None

        try:
            with self.client.post(
                "/v1/chat/completions",
                json=payload,
                catch_response=True,
                timeout=JIUTIAN_CONFIG["timeout"]
            ) as response:

                # 记录首字节时间
                if not first_byte_time:
                    first_byte_time = time.time() - start_time

                if response.status_code == 200:
                    try:
                        result = response.json()
                        if 'choices' in result and len(result['choices']) > 0:
                            response.success()
                            success = True

                            # 提取Token信息
                            token_info = extract_tokens_from_response(result)
                            if not token_info:
                                # 如果API不返回token统计，使用估算方法
                                response_text = extract_response_content(result)
                                token_info = {
                                    'prompt_tokens': estimate_tokens(question),
                                    'completion_tokens': estimate_tokens(response_text),
                                    'total_tokens': estimate_tokens(question + response_text)
                                }
                        else:
                            response.failure("Empty or invalid response")
                            success = False
                    except json.JSONDecodeError:
                        response.failure("Invalid JSON response")
                        success = False
                else:
                    response.failure(f"HTTP {response.status_code}")
                    success = False

        except Exception as e:
            success = False
            print(f"请求异常: {str(e)}")

        # 记录性能指标
        end_time = time.time()
        response_time = end_time - start_time

        performance_metrics.add_result(
            response_time=response_time,
            first_byte_time=first_byte_time,
            success=success,
            concurrent_users=self.environment.runner.user_count if hasattr(self.environment, 'runner') else 1,
            question_type=question_type,
            token_info=token_info
        )
        
        self.question_counts[question_type] += 1
    
    @task(3)
    def simple_question_task(self):
        """简单问题测试任务"""
        question = random.choice(SIMPLE_QUESTIONS)
        self.make_request(question, "simple")
    
    @task(2)
    def medium_question_task(self):
        """中等复杂度问题测试任务"""
        question = random.choice(MEDIUM_QUESTIONS)
        self.make_request(question, "medium")
    
    @task(1)
    def complex_question_task(self):
        """复杂问题测试任务"""
        question = random.choice(COMPLEX_QUESTIONS)
        self.make_request(question, "complex")
    
    def on_stop(self):
        """用户停止时的清理工作"""
        print(f"用户停止 - 问题统计: {self.question_counts}")


class JiutianLoadTestRunner:
    """九天模型压力测试执行器"""

    def __init__(self):
        self.results = {}
        # 生成统一的CSV文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.csv_filename = f"九天模型压力测试报告_{timestamp}.csv"
        self.scenario_count = 0

    def run_test_scenario(self, users, spawn_rate, run_time, scenario_name):
        """运行单个测试场景"""
        print(f"\n{'='*60}")
        print(f"开始执行测试场景: {scenario_name}")
        print(f"并发用户数: {users}, 启动速率: {spawn_rate}/秒, 运行时间: {run_time}秒")
        print(f"模型配置: {MODEL_PARAMS['model']}")
        print(f"{'='*60}")

        # 重置性能指标
        global performance_metrics
        performance_metrics = JiutianPerformanceMetrics()
        performance_metrics.start_time = datetime.now()

        # 设置Locust环境
        env = Environment(user_classes=[JiutianUser])
        env.create_local_runner()

        # 开始测试
        env.runner.start(users, spawn_rate=spawn_rate)

        # 运行指定时间
        start_time = time.time()
        with tqdm(total=run_time, desc=f"执行{scenario_name}") as pbar:
            while time.time() - start_time < run_time:
                time.sleep(1)
                pbar.update(1)

                # 实时显示统计信息
                if int(time.time() - start_time) % 10 == 0:  # 每10秒显示一次
                    current_stats = performance_metrics.get_statistics()
                    if current_stats:
                        pbar.set_postfix({
                            'QPS': f"{current_stats.get('QPS', 0):.1f}",
                            'Token/s': f"{current_stats.get('Token吞吐量(tokens/s)', 0):.0f}",
                            '平均RT': f"{current_stats.get('平均响应时间(秒)', 0):.2f}s",
                            '成功率': f"{current_stats.get('成功率(%)', 0):.1f}%"
                        })

        # 停止测试
        env.runner.stop()
        performance_metrics.end_time = datetime.now()

        # 收集结果
        stats = performance_metrics.get_statistics()
        self.results[scenario_name] = stats

        # 显示结果
        self.print_scenario_results(scenario_name, stats)

        # 保存详细数据到统一的CSV文件中
        self.append_scenario_data_to_csv(scenario_name, performance_metrics)

        return stats

    def append_scenario_data_to_csv(self, scenario_name, metrics):
        """将场景数据追加到统一的CSV文件中"""
        self.scenario_count += 1

        # 为每条详细记录添加场景名称
        detailed_data = []
        for record in metrics.detailed_results:
            record_with_scenario = record.copy()
            record_with_scenario['测试场景'] = scenario_name
            record_with_scenario['场景序号'] = self.scenario_count
            detailed_data.append(record_with_scenario)

        # 转换为DataFrame
        df = pd.DataFrame(detailed_data)

        # 重新排列列的顺序，把场景信息和Token信息放在前面
        if not df.empty:
            cols = ['场景序号', '测试场景', '时间戳', '并发用户数', '响应时间', '首字节时间',
                   '是否成功', '问题类型', '输入tokens', '输出tokens', '总tokens']
            df = df[cols]

        # 追加到CSV文件
        if self.scenario_count == 1:
            # 第一个场景，创建新文件并写入表头
            df.to_csv(self.csv_filename, index=False, encoding='utf-8-sig', mode='w')
            print(f"创建测试报告文件: {self.csv_filename}")
        else:
            # 后续场景，追加数据（不写表头）
            df.to_csv(self.csv_filename, index=False, encoding='utf-8-sig', mode='a', header=False)
            print(f"场景数据已追加到: {self.csv_filename}")

    def print_scenario_results(self, scenario_name, stats):
        """打印场景测试结果"""
        print(f"\n{'-'*50}")
        print(f"测试场景: {scenario_name} - 结果汇总")
        print(f"{'-'*50}")
        print(f"总请求数: {stats.get('总请求数', 0)}")
        print(f"成功请求数: {stats.get('成功请求数', 0)}")
        print(f"失败请求数: {stats.get('失败请求数', 0)}")
        print(f"成功率: {stats.get('成功率(%)', 0):.2f}%")
        print(f"QPS: {stats.get('QPS', 0):.2f}")
        print(f"最大并发用户数: {stats.get('最大并发用户数', 0)}")
        print(f"测试持续时间: {stats.get('测试持续时间(秒)', 0):.2f}秒")
        print(f"\n响应时间统计:")
        print(f"  平均响应时间: {stats.get('平均响应时间(秒)', 0):.3f}秒")
        print(f"  最小响应时间: {stats.get('最小响应时间(秒)', 0):.3f}秒")
        print(f"  最大响应时间: {stats.get('最大响应时间(秒)', 0):.3f}秒")
        print(f"  P50响应时间: {stats.get('P50响应时间(秒)', 0):.3f}秒")
        print(f"  P95响应时间: {stats.get('P95响应时间(秒)', 0):.3f}秒")
        print(f"  P99响应时间: {stats.get('P99响应时间(秒)', 0):.3f}秒")

        if '平均首字节时间(秒)' in stats:
            print(f"\n首字节时间统计:")
            print(f"  平均首字节时间: {stats.get('平均首字节时间(秒)', 0):.3f}秒")
            print(f"  最小首字节时间: {stats.get('最小首字节时间(秒)', 0):.3f}秒")
            print(f"  最大首字节时间: {stats.get('最大首字节时间(秒)', 0):.3f}秒")
            print(f"  P95首字节时间: {stats.get('P95首字节时间(秒)', 0):.3f}秒")

        # Token吞吐量统计
        print(f"\nToken吞吐量统计:")
        print(f"  总输出tokens: {stats.get('总输出tokens', 0):,}")
        print(f"  平均每请求输出tokens: {stats.get('平均每请求输出tokens', 0):.1f}")
        print(f"  Token吞吐量: {stats.get('Token吞吐量(tokens/s)', 0):.2f} tokens/s")
        print(f"  单卡Token吞吐量: {stats.get('单卡Token吞吐量(tokens/s)', 0):.2f} tokens/s")
        print(f"  Token效率: {stats.get('Token效率(tokens/s/用户)', 0):.2f} tokens/s/用户")

    def run_comprehensive_test(self):
        """运行九天模型综合压力测试"""
        print("开始九天模型压力测试")
        print("="*80)
        print(f"测试配置: 16卡GPU, 模型路径: {MODEL_PARAMS['model']}")
        print(f"模型参数: temperature={MODEL_PARAMS['temperature']}, max_tokens={MODEL_PARAMS['max_tokens']}")
        print("="*80)

        # 执行4个测试场景
        for scenario in JIUTIAN_TEST_SCENARIOS:
            try:
                self.run_test_scenario(
                    users=scenario["users"],
                    spawn_rate=scenario["spawn_rate"],
                    run_time=scenario["run_time"],
                    scenario_name=scenario["name"]
                )
                # 场景间休息时间
                print(f"场景完成，休息30秒...")
                time.sleep(30)
            except Exception as e:
                print(f"测试场景 {scenario['name']} 执行失败: {str(e)}")
                continue

        # 生成综合报告
        self.generate_jiutian_report()

    def generate_jiutian_report(self):
        """生成九天模型综合测试报告"""
        if not self.results:
            print("没有测试结果可生成报告")
            return

        print(f"\n{'='*80}")
        print("九天模型压力测试 - 综合报告")
        print(f"{'='*80}")

        # 创建结果对比表
        df_results = pd.DataFrame(self.results).T

        # 显示关键指标对比
        key_metrics = ['最大并发用户数', 'QPS', 'Token吞吐量(tokens/s)', '单卡Token吞吐量(tokens/s)',
                      '平均响应时间(秒)', '成功率(%)', 'P95响应时间(秒)', '平均首字节时间(秒)']
        if not df_results.empty:
            print("\n关键性能指标对比:")
            print("-" * 80)
            for metric in key_metrics:
                if metric in df_results.columns:
                    print(f"{metric:25s}: {df_results[metric].to_dict()}")

        # 分析最佳并发数和最大并发数
        self.analyze_concurrency_performance(df_results)

        # 保存综合报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"九天模型综合性能报告_{timestamp}.csv"
        df_results.to_csv(report_filename, encoding='utf-8-sig')
        print(f"\n综合报告已保存到: {report_filename}")
        print(f"详细测试数据已保存到: {self.csv_filename}")

    def analyze_concurrency_performance(self, df_results):
        """分析并发性能，找出最佳并发数和最大并发数"""
        if df_results.empty or 'QPS' not in df_results.columns:
            return

        print(f"\n{'='*60}")
        print("并发性能分析")
        print(f"{'='*60}")

        # 找出QPS最高的场景（最佳并发数）
        max_qps_scenario = df_results['QPS'].idxmax()
        max_qps_value = df_results.loc[max_qps_scenario, 'QPS']
        best_concurrency = df_results.loc[max_qps_scenario, '最大并发用户数']

        print(f"最佳并发数: {int(best_concurrency)} (QPS: {max_qps_value:.2f})")
        print(f"最佳性能场景: {max_qps_scenario}")

        # 找出Token吞吐量最高的场景
        if 'Token吞吐量(tokens/s)' in df_results.columns:
            max_token_scenario = df_results['Token吞吐量(tokens/s)'].idxmax()
            max_token_value = df_results.loc[max_token_scenario, 'Token吞吐量(tokens/s)']
            token_best_concurrency = df_results.loc[max_token_scenario, '最大并发用户数']

            print(f"最佳Token吞吐量: {max_token_value:.2f} tokens/s (并发数: {int(token_best_concurrency)})")
            print(f"最佳Token吞吐量场景: {max_token_scenario}")

        # 找出成功率>90%的最大并发数
        successful_scenarios = df_results[df_results['成功率(%)'] > 90.0]
        if not successful_scenarios.empty:
            max_successful_concurrency = successful_scenarios['最大并发用户数'].max()
            print(f"最大可承受并发数: {int(max_successful_concurrency)} (成功率>90%)")
        else:
            print("警告: 所有场景成功率都低于90%")

        # GPU效率分析
        gpu_count = 16  # 16卡GPU
        if max_qps_value > 0:
            qps_per_gpu = max_qps_value / gpu_count
            print(f"\nGPU效率分析:")
            print(f"  单卡平均QPS: {qps_per_gpu:.2f}")

            if 'Token吞吐量(tokens/s)' in df_results.columns and max_token_value > 0:
                token_per_gpu = max_token_value / gpu_count
                print(f"  单卡Token吞吐量: {token_per_gpu:.2f} tokens/s")
                print(f"  GPU Token效率评估: {'优秀' if token_per_gpu > 300 else '良好' if token_per_gpu > 150 else '需优化'}")

            print(f"  GPU利用效率评估: {'优秀' if qps_per_gpu > 3 else '良好' if qps_per_gpu > 1.5 else '需优化'}")

        # 性能建议
        print(f"\n性能建议:")
        if max_qps_value < 10:
            print("- QPS较低，建议检查模型推理优化和GPU利用率")
        if 'Token吞吐量(tokens/s)' in df_results.columns and max_token_value < 1000:
            print("- Token吞吐量较低，建议优化模型推理和批处理策略")
        if df_results['平均响应时间(秒)'].max() > 10:
            print("- 响应时间较长，建议优化模型推理速度")
        if df_results['成功率(%)'].min() < 95:
            print("- 部分场景成功率偏低，建议检查系统稳定性")


def main():
    """主函数"""
    print("九天模型压力测试工具")
    print("="*50)

    # 首先测试接口连通性
    print("测试接口连通性...")
    try:
        import requests
        headers = {'Content-Type': 'application/json'}
        payload = {
            **MODEL_PARAMS,
            "messages": [
                {"role": "assistant", "content": "You are a helpful AI assistant."},
                {"role": "user", "content": "你是谁？"}
            ]
        }
        response = requests.post(JIUTIAN_API_URL, headers=headers, json=payload, timeout=30)
        if response.status_code == 200:
            print("✓ 接口连通性测试通过")
        else:
            print(f"✗ 接口连通性测试失败: HTTP {response.status_code}")
            return
    except Exception as e:
        print(f"✗ 接口连通性测试失败: {str(e)}")
        return

    # 创建测试执行器并运行综合测试
    runner = JiutianLoadTestRunner()

    print("\n选择测试模式:")
    print("1. 自动运行综合测试 (推荐)")
    print("2. 启动Locust Web界面")
    print("3. 退出")

    choice = input("请选择 (1-3): ").strip()

    if choice == "1":
        # 综合测试
        runner.run_comprehensive_test()
        print("\n✓ 九天模型压力测试完成!")
    elif choice == "2":
        # Web界面
        print("\n启动Locust Web界面...")
        print("访问 http://localhost:8089 进行手动测试")
        import subprocess
        import sys
        subprocess.run([
            sys.executable, "-m", "locust",
            "-f", "jiutian_load_test.py",
            "--host", JIUTIAN_HOST
        ])
    elif choice == "3":
        print("退出")
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
